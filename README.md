# Tini Desktop App

A modern, cross-platform desktop application built with **Bun**, **Electron**, and **DaisyUI**.

![Tini Desktop App](https://img.shields.io/badge/Bun-000000?style=for-the-badge&logo=bun&logoColor=white)
![Electron](https://img.shields.io/badge/Electron-2B2E3A?style=for-the-badge&logo=electron&logoColor=9FEAF9)
![DaisyUI](https://img.shields.io/badge/DaisyUI-5A0EF8?style=for-the-badge&logo=daisyui&logoColor=white)
![TypeScript](https://img.shields.io/badge/TypeScript-007ACC?style=for-the-badge&logo=typescript&logoColor=white)

## 🚀 Features

- **⚡ Ultra-fast Performance**: Powered by Bun runtime for lightning-fast JavaScript execution
- **🎨 Beautiful UI**: Modern interface built with DaisyUI components and Tailwind CSS
- **🔒 Secure Architecture**: Context isolation and secure IPC communication
- **🌍 Cross-platform**: Runs on Windows, macOS, and Linux
- **🎭 Multiple Themes**: Built-in theme switching with 30+ DaisyUI themes
- **📱 Responsive Design**: Adaptive layout that works on different screen sizes
- **🔧 Developer Friendly**: Hot reload, TypeScript support, and modern tooling

## 🛠️ Technology Stack

### Frontend
- **Electron 36+**: Cross-platform desktop framework
- **DaisyUI 5.0+**: Beautiful CSS components
- **Tailwind CSS 4.x**: Utility-first CSS framework
- **HTML5 & Modern JavaScript**: Latest web standards

### Backend
- **Bun 1.2+**: Fast JavaScript runtime
- **TypeScript**: Type-safe development
- **RESTful API**: Standard HTTP API design
- **In-memory Storage**: Simple data persistence

## 📦 Installation

### Prerequisites
- [Bun](https://bun.sh/) (latest version)
- Node.js 16+ (for Electron compatibility)

### Quick Start

1. **Install dependencies**
   ```bash
   bun install
   ```

2. **Start development servers**
   ```bash
   # Start both frontend and backend
   bun run dev

   # Or start them separately
   bun run dev:backend    # Start Bun server
   bun run dev:frontend   # Start Electron app
   ```

3. **Build for production**
   ```bash
   bun run build
   ```

## 🎯 Usage

### Development Commands

```bash
# Development
bun run dev              # Start both frontend and backend
bun run dev:backend      # Start Bun server only (http://localhost:3001)
bun run dev:frontend     # Start Electron app only

# Building
bun run build            # Build entire application
bun run build:css        # Compile Tailwind CSS
bun run build:backend    # Build backend server

# Testing
bun test                 # Run test suite
bun run electron         # Run production Electron app
bun run electron:dev     # Run Electron in development mode
```

## 🏗️ Project Structure

```
tini-desktop-app/
├── src/
│   ├── main/           # Electron main process
│   ├── renderer/       # Frontend application
│   ├── backend/        # Bun server
│   └── shared/         # Shared utilities
├── dist/               # Built application
├── docs/               # Documentation
└── config files        # Build and development configuration
```

## 📚 Documentation

- [Project Brief](docs/PROJECT_BRIEF.md) - Comprehensive project overview
- [Task Checklist](docs/TASK_CHECKLIST.md) - Development progress tracking

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Commit your changes
4. Push to the branch
5. Open a Pull Request

## 📝 License

This project is licensed under the MIT License.

---

**Made with ❤️ using Bun, Electron, and DaisyUI**
