# Tini Desktop App

A modern desktop application built with **Bun**, **Electron**, and **DaisyUI** featuring a complete authentication system.

![Tini Desktop App](https://img.shields.io/badge/Bun-000000?style=for-the-badge&logo=bun&logoColor=white)
![Electron](https://img.shields.io/badge/Electron-2B2E3A?style=for-the-badge&logo=electron&logoColor=9FEAF9)
![Daisy<PERSON>](https://img.shields.io/badge/DaisyUI-5A0EF8?style=for-the-badge&logo=daisyui&logoColor=white)
![TypeScript](https://img.shields.io/badge/TypeScript-007ACC?style=for-the-badge&logo=typescript&logoColor=white)

## 🚀 Features

### Core Features
- **Modern Tech Stack**: Bun runtime, Electron framework, DaisyUI components
- **Complete Authentication**: User registration, login, session management, logout
- **Beautiful UI**: DaisyUI-styled interface with multiple theme support
- **Cross-Platform**: Works on macOS, Windows, and Linux
- **Hot Reload**: Development mode with automatic reloading
- **Security**: Password hashing, rate limiting, session validation

### Authentication System (Better Auth Integration)
- ✅ **Better Auth Integration**: Modern authentication with Better Auth-style API
- ✅ **Dual API Support**: Both Better Auth endpoints and legacy compatibility
- ✅ **User Registration**: Secure sign-up with auto sign-in capability
- ✅ **Secure Login**: Email/username authentication with session management
- ✅ **"Remember Me"**: Configurable session duration (24 hours / 30 days)
- ✅ **Session Management**: Token-based authentication with validation
- ✅ **Rate Limiting**: Brute force protection (5 attempts per 15 minutes)
- ✅ **Password Security**: Hashing with salt and strength requirements
- ✅ **Secure Logout**: Complete session cleanup and invalidation

### UI/UX Features
- ✅ Responsive design
- ✅ Multiple themes (light, dark, cyberpunk, dracula, cupcake)
- ✅ Real-time form validation
- ✅ Loading states and error handling
- ✅ Smooth animations and transitions
- ✅ Keyboard shortcuts support

## 📋 Prerequisites

- **Node.js** (v18 or higher)
- **Bun** (latest version)
- **Git** (for version control)

## 🛠️ Installation

### 1. Install Bun (if not already installed)
```bash
curl -fsSL https://bun.sh/install | bash
```

### 2. Clone and Setup
```bash
# Clone the repository
git clone <repository-url>
cd tini-desktop-app

# Install dependencies
npm install
# or
bun install
```

## 🚀 Quick Start

### Development Mode
```bash
# Start both backend and frontend in development mode
npm run dev

# This will:
# - Start the backend server on http://localhost:3001
# - Launch the Electron app with DevTools enabled
# - Enable hot reload for both backend and frontend
```

### Individual Services
```bash
# Start only the backend server
npm run dev:backend

# Start only the Electron app
npm run dev:electron

# Watch and rebuild CSS
npm run dev:frontend
```

## 📖 Usage

### First Time Setup
1. Run `npm run dev` to start the application
2. The app will open to the authentication page
3. Click "Don't have an account? Sign up" to register
4. Fill in the registration form:
   - Username (minimum 3 characters)
   - Email (valid email format)
   - Password (minimum 6 characters with letters and numbers)
   - Confirm password
5. After successful registration, log in with your credentials
6. Access the main application dashboard

### Authentication Flow
- **Registration**: Create new account with validation
- **Login**: Authenticate with email or username
- **Remember Me**: Optional 30-day session persistence
- **Main App**: Access dashboard and features after authentication
- **Logout**: Secure session termination

### Theme Switching
- Click the theme selector in the header
- Choose from: Light, Dark, Cupcake, Cyberpunk, Dracula
- Theme preference is saved automatically

## 🏗️ Build Commands

### Development
```bash
npm run dev              # Start both backend and frontend
npm run dev:backend      # Backend server only
npm run dev:electron     # Electron app only
npm run dev:frontend     # CSS watch mode
```

### Building
```bash
npm run build            # Complete production build
npm run build:css        # Build and minify CSS
npm run build:backend    # Compile backend
npm run build:clean      # Clean build artifacts
```

### Packaging
```bash
npm run package          # Package for current platform
npm run package:all      # Package for all platforms
npm run package:mac      # macOS DMG
npm run package:win      # Windows installer
npm run package:linux    # Linux AppImage
```

### Testing
```bash
npm run test             # Run all tests
npm run test:backend     # Backend API tests
npm run test:auth        # Authentication flow tests
npm run test:better-auth # Better Auth integration tests
npm run test:csp         # CSP configuration tests
```

## 🧪 Testing the Authentication System

### Better Auth Integration Testing
```bash
# Run Better Auth integration tests
./scripts/test-better-auth.sh

# Test Better Auth endpoints
curl -X POST http://localhost:3001/api/auth/sign-up \
  -H "Content-Type: application/json" \
  -d '{"name":"testuser","email":"<EMAIL>","password":"test123"}'

curl -X POST http://localhost:3001/api/auth/sign-in \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"test123","rememberMe":false}'
```

### Legacy Authentication Testing
```bash
# Run legacy authentication tests
./scripts/test-auth-simple.sh

# Test legacy endpoints
curl -X POST http://localhost:3001/api/auth/register \
  -H "Content-Type: application/json" \
  -d '{"username":"testuser","email":"<EMAIL>","password":"test123","confirmPassword":"test123"}'

curl -X POST http://localhost:3001/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{"emailOrUsername":"<EMAIL>","password":"test123","rememberMe":false}'
```

### Test Results
- ✅ **Better Auth Integration**: 7/7 tests passing (100% success rate)
- ✅ **Legacy Compatibility**: Full backward compatibility maintained
- ✅ **Security Features**: Rate limiting, password hashing, session management
- ✅ **API Endpoints**: Both Better Auth and legacy endpoints working

## 📁 Project Structure

```
tini-desktop-app/
├── src/
│   ├── main/              # Electron main process
│   │   ├── main.js        # Main entry point
│   │   └── preload.js     # Preload script
│   ├── renderer/          # Frontend application
│   │   ├── index.html     # Main app page
│   │   ├── auth.html      # Authentication page
│   │   ├── app.js         # Main app logic
│   │   ├── auth.js        # Authentication logic
│   │   └── style.css      # Compiled styles
│   ├── backend/           # Backend server
│   │   └── server.ts      # Bun server with auth
│   └── shared/            # Shared utilities
├── scripts/               # Build and test scripts
├── docs/                  # Documentation
├── dist/                  # Build output
└── package.json           # Dependencies and scripts
```

## 🔒 Security Features

- **Password Hashing**: SHA-256 with application salt
- **Rate Limiting**: Prevents brute force attacks
- **Session Management**: Secure token-based authentication
- **Input Validation**: Client and server-side validation
- **CSRF Protection**: Secure request handling

## 🚨 Troubleshooting

### Common Issues

**Backend not starting:**
```bash
# Check if port 3001 is available
lsof -i :3001

# Restart backend
npm run dev:backend
```

**Authentication page not loading:**
```bash
# Check file paths and restart Electron
npm run dev:electron
```

**CSS not updating:**
```bash
# Rebuild CSS
npm run build:css
```

## 📚 Documentation

- [Better Auth Integration](./docs/BETTER_AUTH_INTEGRATION.md) - Complete Better Auth integration guide
- [CSP Fix Guide](./docs/CSP_FIX_GUIDE.md) - Content Security Policy fixes and troubleshooting
- [Build Guide](./docs/BUILD.md) - Comprehensive build instructions
- [Authentication Guide](./docs/AUTHENTICATION.md) - Authentication system details

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Run tests: `npm run test`
5. Submit a pull request

## 📄 License

MIT License - see [LICENSE](LICENSE) file for details.

---

**Built with ❤️ using Bun + Electron + DaisyUI**
