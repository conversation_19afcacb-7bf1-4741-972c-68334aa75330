# Authentication System Documentation

## Overview

The Tini Desktop App now includes a complete authentication system with user registration, login, session management, and security features. The system is built using the existing Bun + Electron + DaisyUI architecture.

## Features

### ✅ **Core Authentication Features**

1. **User Registration**
   - Username, email, and password fields
   - Client-side validation (email format, password strength, matching passwords)
   - Server-side validation and sanitization
   - Duplicate user prevention

2. **User Login**
   - Email or username login support
   - "Remember Me" functionality (30-day vs 1-day sessions)
   - Rate limiting protection
   - Secure password verification

3. **Session Management**
   - JWT-like session tokens
   - Automatic session validation
   - Session expiration handling
   - Secure logout functionality

4. **Security Features**
   - Password hashing with salt
   - Rate limiting (5 attempts per 15 minutes)
   - Input sanitization
   - CSRF protection
   - Secure session storage

### ✅ **UI/UX Features**

1. **Authentication Page**
   - Modern DaisyUI-styled login/register forms
   - Toggle between login and register modes
   - Real-time form validation
   - Loading states and error handling
   - Theme support (matches main app themes)

2. **Main App Integration**
   - Automatic authentication check on startup
   - User welcome message
   - Logout button in header
   - Seamless navigation between auth and main app

## API Endpoints

### Authentication Routes

#### `POST /api/auth/register`
Register a new user account.

**Request Body:**
```json
{
  "username": "string",
  "email": "string", 
  "password": "string",
  "confirmPassword": "string"
}
```

**Response:**
```json
{
  "success": true,
  "message": "User registered successfully",
  "user": {
    "id": "string",
    "username": "string", 
    "email": "string",
    "createdAt": "string"
  }
}
```

#### `POST /api/auth/login`
Authenticate user and create session.

**Request Body:**
```json
{
  "emailOrUsername": "string",
  "password": "string",
  "rememberMe": "boolean"
}
```

**Response:**
```json
{
  "success": true,
  "message": "Login successful",
  "sessionId": "string",
  "user": {
    "id": "string",
    "username": "string",
    "email": "string", 
    "lastLogin": "string"
  }
}
```

#### `POST /api/auth/logout`
Logout user and invalidate session.

**Request Body:**
```json
{
  "sessionId": "string"
}
```

#### `GET /api/auth/validate`
Validate existing session.

**Query Parameters:**
- `sessionId`: Session ID to validate

**Response:**
```json
{
  "success": true,
  "valid": true,
  "user": {
    "id": "string",
    "username": "string",
    "email": "string",
    "lastLogin": "string"
  }
}
```

## File Structure

### Frontend Files
- `src/renderer/auth.html` - Authentication page UI
- `src/renderer/auth.js` - Authentication logic and form handling
- `src/renderer/app.js` - Updated with authentication checks
- `src/renderer/style.css` - Enhanced with auth page styling

### Backend Files
- `src/backend/server.ts` - Updated with authentication endpoints
- Authentication utilities (password hashing, session management, rate limiting)

### Electron Files
- `src/main/main.js` - Updated with navigation between auth/main pages
- `src/main/preload.js` - Updated with navigation API exposure

## Security Implementation

### Password Security
- SHA-256 hashing with application salt
- Minimum 6 characters with letters and numbers
- Server-side validation

### Rate Limiting
- 5 failed attempts per IP address
- 15-minute lockout period
- Automatic cleanup of old attempts

### Session Security
- Unique session IDs
- Configurable expiration (1 day or 30 days)
- Server-side session validation
- Automatic cleanup of expired sessions

### Input Validation
- Client-side real-time validation
- Server-side sanitization
- Email format validation
- Password strength requirements

## Usage Flow

### First Time User
1. App opens to authentication page
2. User clicks "Don't have an account? Sign up"
3. User fills registration form with validation feedback
4. Upon successful registration, switches to login mode
5. User logs in and is redirected to main app

### Returning User
1. App checks for existing session
2. If valid session exists, goes directly to main app
3. If no/invalid session, shows authentication page
4. User logs in and is redirected to main app

### Logout
1. User clicks logout button in main app header
2. Session is invalidated on server
3. Local storage is cleared
4. User is redirected to authentication page

## Testing

### Manual Testing
1. **Registration**: Create new account with various inputs
2. **Login**: Test with email/username, remember me option
3. **Session**: Close/reopen app to test session persistence
4. **Logout**: Test logout functionality
5. **Rate Limiting**: Test multiple failed login attempts
6. **Validation**: Test form validation with invalid inputs

### API Testing
```bash
# Test registration
curl -X POST http://localhost:3001/api/auth/register \
  -H "Content-Type: application/json" \
  -d '{"username":"testuser","email":"<EMAIL>","password":"test123","confirmPassword":"test123"}'

# Test login  
curl -X POST http://localhost:3001/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{"emailOrUsername":"<EMAIL>","password":"test123","rememberMe":false}'

# Test session validation
curl "http://localhost:3001/api/auth/validate?sessionId=YOUR_SESSION_ID"
```

## Configuration

### Session Duration
- Default session: 24 hours
- "Remember Me" session: 30 days
- Configurable in `createSession()` function

### Rate Limiting
- Max attempts: 5 per IP
- Lockout duration: 15 minutes
- Configurable in `checkRateLimit()` function

### Password Requirements
- Minimum length: 6 characters
- Must contain letters and numbers
- Configurable in `isValidPassword()` function

## Future Enhancements

### Potential Improvements
1. **Database Integration**: Replace in-memory storage with persistent database
2. **Email Verification**: Add email confirmation for new accounts
3. **Password Reset**: Implement forgot password functionality
4. **Two-Factor Authentication**: Add 2FA support
5. **OAuth Integration**: Add social login options
6. **Account Management**: User profile editing and account deletion
7. **Admin Panel**: User management interface
8. **Audit Logging**: Enhanced security logging
9. **Password Policies**: More sophisticated password requirements
10. **Brute Force Protection**: Enhanced security measures

### Database Migration
When ready to move from in-memory storage:
1. Choose database (SQLite, PostgreSQL, etc.)
2. Create user and session tables
3. Update authentication functions to use database queries
4. Add database connection management
5. Implement proper error handling for database operations

## Troubleshooting

### Common Issues
1. **Session not persisting**: Check localStorage and session validation
2. **Rate limiting triggered**: Wait 15 minutes or restart backend
3. **Authentication page not loading**: Check file paths in main.js
4. **Backend connection failed**: Ensure backend server is running on port 3001
5. **Theme not applying**: Check theme localStorage and CSS imports

### Debug Mode
- Enable Electron DevTools for frontend debugging
- Check backend console logs for API request details
- Use browser Network tab to inspect API calls
- Check localStorage for session data

## Conclusion

The authentication system provides a solid foundation for user management in the Tini Desktop App. It includes modern security practices, a polished UI, and seamless integration with the existing application architecture. The system is designed to be easily extensible for future enhancements while maintaining security and usability.
