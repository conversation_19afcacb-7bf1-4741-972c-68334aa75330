{"version": 3, "sources": ["dist/base64.cjs"], "names": ["global", "factory", "exports", "module", "define", "amd", "globalThis", "self", "base64", "this", "chars", "charsUrl", "gen<PERSON><PERSON><PERSON>", "lookupTemp", "Uint8Array", "len", "length", "let", "i", "target", "charCodeAt", "lookup", "lookupUrl", "base64UrlPattern", "base64Pattern", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "data", "urlMode", "bufferLength", "p", "encoded1", "encoded2", "encoded3", "encoded4", "arraybuffer", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "bytes", "fromArrayBuffer", "a<PERSON><PERSON><PERSON>", "result", "remainder", "substring", "toString", "str", "TextDecoder", "decode", "fromString", "TextEncoder", "encode", "validate", "encoded", "String", "test", "_e"], "mappings": "CAAA,SAAWA,OAAQC,SAClB,OAAOC,UAAY,UAAY,OAAOC,SAAW,YAAcA,OAAOD,QAAUD,QAAQ,EACxF,OAAOG,SAAW,YAAcA,OAAOC,IAAMD,OAAOH,OAAO,GAC1DD,OAAS,OAAOM,aAAe,YAAcA,WAAaN,QAAUO,KAAMP,OAAOQ,OAASP,QAAQ,EACnG,GAAEQ,KAAM,WAAe,aA8BvB,MAECC,MAAQ,mEAGRC,SAAW,mEAEXC,UAAY,SACX,MAAMC,WAAa,OAAOC,aAAe,YAAc,GAAK,IAAIA,WAAW,GAAG,EAC9E,MAAMC,IAAML,MAAMM,OAClB,IAAKC,IAAIC,EAAI,EAAGA,EAAIH,IAAKG,CAAC,GAAI,CAC7BL,WAAWM,OAAOC,WAAWF,CAAC,GAAKA,CACpC,CACA,OAAOL,UACR,EAGAQ,OAAST,UAAUF,KAAK,EACxBY,UAAYV,UAAUD,QAAQ,EAK/B,MAAMY,iBAAmB,qBACzB,MAAMC,cAAgB,0BAKtB,MAAMhB,OAAS,GAUfA,OAAOiB,cAAgB,CAACC,KAAMC,WAC7B,MACCZ,IAAMW,KAAKV,OACZC,IAAIW,aAAeF,KAAKV,OAAS,IAChCE,EACAW,EAAI,EACJC,SACAC,SACAC,SACAC,SAED,GAAIP,KAAKA,KAAKV,OAAS,KAAO,IAAK,CAClCY,YAAY,GACZ,GAAIF,KAAKA,KAAKV,OAAS,KAAO,IAAK,CAClCY,YAAY,EACb,CACD,CAEA,MACCM,YAAc,IAAIC,YAAYP,YAAY,EAC1CQ,MAAQ,IAAItB,WAAWoB,WAAW,EAClCf,OAASQ,QAAUL,UAAYD,OAEhC,IAAKH,EAAI,EAAGA,EAAIH,IAAKG,GAAK,EAAG,CAC5BY,SAAWX,OAAOO,KAAKN,WAAWF,CAAC,GACnCa,SAAWZ,OAAOO,KAAKN,WAAWF,EAAI,CAAC,GACvCc,SAAWb,OAAOO,KAAKN,WAAWF,EAAI,CAAC,GACvCe,SAAWd,OAAOO,KAAKN,WAAWF,EAAI,CAAC,GAEvCkB,MAAMP,CAAC,IAAOC,UAAY,EAAMC,UAAY,EAC5CK,MAAMP,CAAC,KAAQE,SAAW,KAAO,EAAMC,UAAY,EACnDI,MAAMP,CAAC,KAAQG,SAAW,IAAM,EAAMC,SAAW,EAClD,CAEA,OAAOC,WAER,EAUA1B,OAAO6B,gBAAkB,CAACC,OAAQX,WACjC,MAAMS,MAAQ,IAAItB,WAAWwB,MAAM,EACnCrB,IACCC,EACAqB,OAAS,GAEV,MACCxB,IAAMqB,MAAMpB,OACZG,OAASQ,QAAUhB,SAAWD,MAE/B,IAAKQ,EAAI,EAAGA,EAAIH,IAAKG,GAAK,EAAG,CAC5BqB,QAAUpB,OAAOiB,MAAMlB,IAAM,GAC7BqB,QAAUpB,QAASiB,MAAMlB,GAAK,IAAM,EAAMkB,MAAMlB,EAAI,IAAM,GAC1DqB,QAAUpB,QAASiB,MAAMlB,EAAI,GAAK,KAAO,EAAMkB,MAAMlB,EAAI,IAAM,GAC/DqB,QAAUpB,OAAOiB,MAAMlB,EAAI,GAAK,GACjC,CAEA,MAAMsB,UAAYzB,IAAM,EACxB,GAAIyB,YAAc,EAAG,CACpBD,OAASA,OAAOE,UAAU,EAAGF,OAAOvB,OAAS,CAAC,GAAKW,QAAU,GAAK,IACnE,MAAO,GAAIa,YAAc,EAAG,CAC3BD,OAASA,OAAOE,UAAU,EAAGF,OAAOvB,OAAS,CAAC,GAAKW,QAAU,GAAK,KACnE,CAEA,OAAOY,MAER,EAUA/B,OAAOkC,SAAW,CAACC,IAAKhB,WACvB,OAAO,IAAIiB,aAAcC,OAAOrC,OAAOiB,cAAckB,IAAKhB,OAAO,CAAC,CACnE,EAUAnB,OAAOsC,WAAa,CAACH,IAAKhB,WACzB,OAAOnB,OAAO6B,iBAAgB,IAAIU,aAAcC,OAAOL,GAAG,EAAGhB,OAAO,CACrE,EASAnB,OAAOyC,SAAW,CAACC,QAASvB,WAG3B,GAAI,EAAE,OAAOuB,UAAY,UAAYA,mBAAmBC,QAAS,CAChE,OAAO,KACR,CAGA,IACC,OAAOxB,QAAUJ,iBAAiB6B,KAAKF,OAAO,EAAI1B,cAAc4B,KAAKF,OAAO,CAG7E,CAFE,MAAOG,IACR,OAAO,KACR,CACD,EAEA7C,OAAOA,OAASA,OAEhB,OAAOA,MAEP,CAAC"}