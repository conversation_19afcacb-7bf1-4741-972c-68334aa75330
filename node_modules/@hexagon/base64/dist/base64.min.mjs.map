{"version": 3, "sources": ["dist/base64.mjs"], "names": ["chars", "charsUrl", "gen<PERSON><PERSON><PERSON>", "lookupTemp", "Uint8Array", "len", "length", "let", "i", "target", "charCodeAt", "lookup", "lookupUrl", "base64UrlPattern", "base64Pattern", "base64", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "data", "urlMode", "bufferLength", "p", "encoded1", "encoded2", "encoded3", "encoded4", "arraybuffer", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "bytes", "fromArrayBuffer", "a<PERSON><PERSON><PERSON>", "result", "remainder", "substring", "toString", "str", "TextDecoder", "decode", "fromString", "TextEncoder", "encode", "validate", "encoded", "String", "test", "_e"], "mappings": "AA4BA,MAECA,MAAQ,mEAGRC,SAAW,mEAEXC,UAAY,SACX,MAAMC,WAAa,OAAOC,aAAe,YAAc,GAAK,IAAIA,WAAW,GAAG,EAC9E,MAAMC,IAAML,MAAMM,OAClB,IAAKC,IAAIC,EAAI,EAAGA,EAAIH,IAAKG,CAAC,GAAI,CAC7BL,WAAWM,OAAOC,WAAWF,CAAC,GAAKA,CACpC,CACA,OAAOL,UACR,EAGAQ,OAAST,UAAUF,KAAK,EACxBY,UAAYV,UAAUD,QAAQ,EAK/B,MAAMY,iBAAmB,qBACzB,MAAMC,cAAgB,0BAKtB,MAAMC,OAAS,GAUfA,OAAOC,cAAgB,CAACC,KAAMC,WAC7B,MACCb,IAAMY,KAAKX,OACZC,IAAIY,aAAeF,KAAKX,OAAS,IAChCE,EACAY,EAAI,EACJC,SACAC,SACAC,SACAC,SAED,GAAIP,KAAKA,KAAKX,OAAS,KAAO,IAAK,CAClCa,YAAY,GACZ,GAAIF,KAAKA,KAAKX,OAAS,KAAO,IAAK,CAClCa,YAAY,EACb,CACD,CAEA,MACCM,YAAc,IAAIC,YAAYP,YAAY,EAC1CQ,MAAQ,IAAIvB,WAAWqB,WAAW,EAClChB,OAASS,QAAUN,UAAYD,OAEhC,IAAKH,EAAI,EAAGA,EAAIH,IAAKG,GAAK,EAAG,CAC5Ba,SAAWZ,OAAOQ,KAAKP,WAAWF,CAAC,GACnCc,SAAWb,OAAOQ,KAAKP,WAAWF,EAAI,CAAC,GACvCe,SAAWd,OAAOQ,KAAKP,WAAWF,EAAI,CAAC,GACvCgB,SAAWf,OAAOQ,KAAKP,WAAWF,EAAI,CAAC,GAEvCmB,MAAMP,CAAC,IAAOC,UAAY,EAAMC,UAAY,EAC5CK,MAAMP,CAAC,KAAQE,SAAW,KAAO,EAAMC,UAAY,EACnDI,MAAMP,CAAC,KAAQG,SAAW,IAAM,EAAMC,SAAW,EAClD,CAEA,OAAOC,WAER,EAUAV,OAAOa,gBAAkB,CAACC,OAAQX,WACjC,MAAMS,MAAQ,IAAIvB,WAAWyB,MAAM,EACnCtB,IACCC,EACAsB,OAAS,GAEV,MACCzB,IAAMsB,MAAMrB,OACZG,OAASS,QAAUjB,SAAWD,MAE/B,IAAKQ,EAAI,EAAGA,EAAIH,IAAKG,GAAK,EAAG,CAC5BsB,QAAUrB,OAAOkB,MAAMnB,IAAM,GAC7BsB,QAAUrB,QAASkB,MAAMnB,GAAK,IAAM,EAAMmB,MAAMnB,EAAI,IAAM,GAC1DsB,QAAUrB,QAASkB,MAAMnB,EAAI,GAAK,KAAO,EAAMmB,MAAMnB,EAAI,IAAM,GAC/DsB,QAAUrB,OAAOkB,MAAMnB,EAAI,GAAK,GACjC,CAEA,MAAMuB,UAAY1B,IAAM,EACxB,GAAI0B,YAAc,EAAG,CACpBD,OAASA,OAAOE,UAAU,EAAGF,OAAOxB,OAAS,CAAC,GAAKY,QAAU,GAAK,IACnE,MAAO,GAAIa,YAAc,EAAG,CAC3BD,OAASA,OAAOE,UAAU,EAAGF,OAAOxB,OAAS,CAAC,GAAKY,QAAU,GAAK,KACnE,CAEA,OAAOY,MAER,EAUAf,OAAOkB,SAAW,CAACC,IAAKhB,WACvB,OAAO,IAAIiB,aAAcC,OAAOrB,OAAOC,cAAckB,IAAKhB,OAAO,CAAC,CACnE,EAUAH,OAAOsB,WAAa,CAACH,IAAKhB,WACzB,OAAOH,OAAOa,iBAAgB,IAAIU,aAAcC,OAAOL,GAAG,EAAGhB,OAAO,CACrE,EASAH,OAAOyB,SAAW,CAACC,QAASvB,WAG3B,GAAI,EAAE,OAAOuB,UAAY,UAAYA,mBAAmBC,QAAS,CAChE,OAAO,KACR,CAGA,IACC,OAAOxB,QAAUL,iBAAiB8B,KAAKF,OAAO,EAAI3B,cAAc6B,KAAKF,OAAO,CAG7E,CAFE,MAAOG,IACR,OAAO,KACR,CACD,EAEA7B,OAAOA,OAASA,cAEPA,OAAQA,iBAAmB"}