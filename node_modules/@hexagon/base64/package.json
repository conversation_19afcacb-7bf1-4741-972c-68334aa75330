{"name": "@hexagon/base64", "version": "1.1.28", "description": "Base64 and base64url to string or arraybuffer, and back. Node, Deno or browser.", "author": "Hexagon <github.com/hexagon>", "contributors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://hertzen.com"}], "homepage": "https://base64.56k.guru", "repository": {"type": "git", "url": "https://github.com/hexagon/base64"}, "bugs": {"url": "https://github.com/hexagon/base64/issues"}, "files": ["dist/*", "src/*", "types/*", "SECURITY.md"], "keywords": ["base64", "base64url", "parser", "base64", "isomorphic", "arraybuffer", "string"], "scripts": {"test": "uvu test test.base64.js", "test:dist": "uvu test/node/js && npm run test:ts", "test:coverage": "c8 --include=src npm test", "test:lint": "eslint ./**/*.js ./**/*.cjs", "test:lint:fix": "eslint --fix  ./**/*.js ./**/*.cjs", "test:ts": "tsc --strict --noEmit ./test/node/ts/basics.ts", "build": "npm update && npm run build:precleanup && npm run test:lint && npm run build:typings && npm run build:dist && npm run build:minify && npm run build:cleanup && npm run test:coverage && npm run test:dist", "build:ci": "npm run test:lint && npm run build:typings && npm run build:dist && npm run build:minify && npm run build:cleanup && npm run test:coverage && npm run test:dist", "build:precleanup": "(rm -rf types/* || del /Q types\\*) && (rm -rf dist/* || del /Q dist\\*)", "build:dist": "rollup -c ./rollup.config.js", "build:minify": "uglifyjs dist/base64.cjs --source-map -o dist/base64.min.js && uglifyjs dist/base64.mjs --source-map -o dist/base64.min.mjs", "build:typings": "tsc", "build:cleanup": "(rm dist/base64.mjs || del dist\\base64.mjs)"}, "type": "module", "main": "./dist/base64.cjs", "browser": "./dist/base64.min.js", "module": "./src/base64.js", "types": "types/base64.single.d.ts", "exports": {".": {"import": "./src/base64.js", "require": "./dist/base64.cjs", "browser": "./dist/base64.min.js"}}, "devDependencies": {"c8": "^8.0.1", "eslint": "^8.46.0", "jsdoc": "^4.0.2", "rollup": "^3.27.2", "typescript": "^5.2.2", "uglify-js": "^3.17.4", "uvu": "^0.5.6"}, "license": "MIT"}