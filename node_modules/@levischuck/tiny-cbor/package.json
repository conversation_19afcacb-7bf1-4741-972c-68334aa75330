{"name": "@levischuck/tiny-cbor", "version": "0.2.11", "description": "Tiny CBOR library", "repository": {"type": "git", "url": "git+https://github.com/levischuck/tiny-cbor.git"}, "license": "MIT", "bugs": {"url": "https://github.com/levischuck/tiny-cbor/issues"}, "main": "./script/index.js", "module": "./esm/index.js", "exports": {".": {"import": "./esm/index.js", "require": "./script/index.js"}}, "devDependencies": {"@types/node": "^20.9.0"}, "_generatedBy": "dnt@0.40.0"}