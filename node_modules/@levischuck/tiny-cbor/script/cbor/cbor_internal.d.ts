export declare function decodeLength(data: DataView, argument: number, index: number): [number, number];
export type MajorType = 0 | 1 | 2 | 3 | 4 | 5 | 6 | 7;
export declare const MAJOR_TYPE_UNSIGNED_INTEGER: MajorType;
export declare const MAJOR_TYPE_NEGATIVE_INTEGER: MajorType;
export declare const MAJOR_TYPE_BYTE_STRING: MajorType;
export declare const MAJOR_TYPE_TEXT_STRING: MajorType;
export declare const MAJOR_TYPE_ARRAY: MajorType;
export declare const MAJOR_TYPE_MAP: MajorType;
export declare const MAJOR_TYPE_TAG: MajorType;
export declare const MAJOR_TYPE_SIMPLE_OR_FLOAT: MajorType;
export declare function encodeLength(major: MajorType, argument: number | bigint): number[];
