# `@peculiar/asn1-ecc`

[![License](https://img.shields.io/badge/license-MIT-green.svg?style=flat)](https://raw.githubusercontent.com/PeculiarVentures/asn1-schema/master/packages/ecc/LICENSE.md)
[![npm version](https://badge.fury.io/js/%40peculiar%2Fasn1-ecc.svg)](https://badge.fury.io/js/%40peculiar%2Fasn1-ecc)

[![NPM](https://nodei.co/npm/@peculiar/asn1-ecc.png)](https://nodei.co/npm/@peculiar/asn1-ecc/)

This module provides ASN.1 schema definitions and parsers for Elliptic Curve Cryptography (ECC) structures. It includes support for various ECC-related standards and specifications, making it easier to work with ECC keys and certificates in JavaScript and TypeScript applications.

| Document                                                                | Description                                                                                                  |
| ----------------------------------------------------------------------- | ------------------------------------------------------------------------------------------------------------ |
| [RFC 5915](https://tools.ietf.org/html/rfc5915)                         | Elliptic Curve Private Key Structure                                                                         |
| [RFC 5480](https://tools.ietf.org/html/rfc5480)                         | Elliptic Curve Cryptography Subject Public Key Information                                                   |
| [RFC 3279](https://datatracker.ietf.org/doc/html/rfc3279#section-2.3.5) | Algorithms and Identifiers for the Internet X.509 Public Key Infrastructure Certificate: ECDSA and ECDH Keys |
