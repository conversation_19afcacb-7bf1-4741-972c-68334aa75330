import { AlgorithmIdentifier } from "@peculiar/asn1-x509";
/**
 * ECDSA with SHA-1
 * Parameters are ABSENT
 */
export declare const ecdsaWithSHA1: AlgorithmIdentifier;
/**
 * ECDSA with SHA-224. Parameters are ABSENT
 */
export declare const ecdsaWithSHA224: AlgorithmIdentifier;
/**
 * ECDSA with SHA-256. Parameters are ABSENT
 */
export declare const ecdsaWithSHA256: AlgorithmIdentifier;
/**
 * ECDSA with SHA-384. Parameters are ABSENT
 */
export declare const ecdsaWithSHA384: AlgorithmIdentifier;
/**
 * ECDSA with SHA-512. Parameters are ABSENT
 */
export declare const ecdsaWithSHA512: AlgorithmIdentifier;
