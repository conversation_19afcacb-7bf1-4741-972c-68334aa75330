import { AlgorithmIdentifier } from "@peculiar/asn1-x509";
export declare const md2: AlgorithmIdentifier;
export declare const md4: AlgorithmIdentifier;
export declare const sha1: AlgorithmIdentifier;
export declare const sha224: AlgorithmIdentifier;
export declare const sha256: AlgorithmIdentifier;
export declare const sha384: AlgorithmIdentifier;
export declare const sha512: AlgorithmIdentifier;
export declare const sha512_224: AlgorithmIdentifier;
export declare const sha512_256: AlgorithmIdentifier;
export declare const mgf1SHA1: AlgorithmIdentifier;
export declare const pSpecifiedEmpty: AlgorithmIdentifier;
export declare const rsaEncryption: AlgorithmIdentifier;
export declare const md2WithRSAEncryption: AlgorithmIdentifier;
export declare const md5WithRSAEncryption: AlgorithmIdentifier;
export declare const sha1WithRSAEncryption: AlgorithmIdentifier;
export declare const sha224WithRSAEncryption: AlgorithmIdentifier;
export declare const sha256WithRSAEncryption: AlgorithmIdentifier;
export declare const sha384WithRSAEncryption: AlgorithmIdentifier;
export declare const sha512WithRSAEncryption: AlgorithmIdentifier;
export declare const sha512_224WithRSAEncryption: AlgorithmIdentifier;
export declare const sha512_256WithRSAEncryption: AlgorithmIdentifier;
