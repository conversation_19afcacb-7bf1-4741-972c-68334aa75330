cmd_Release/obj.target/better_sqlite3/src/better_sqlite3.o := c++ -o Release/obj.target/better_sqlite3/src/better_sqlite3.o ../src/better_sqlite3.cpp '-DNODE_GYP_MODULE_NAME=better_sqlite3' '-DUSING_UV_SHARED=1' '-DUSING_V8_SHARED=1' '-DV8_DEPRECATION_WARNINGS=1' '-DV8_DEPRECATION_WARNINGS' '-DV8_IMMINENT_DEPRECATION_WARNINGS' '-D_GLIBCXX_USE_CXX11_ABI=1' '-D_DARWIN_USE_64_BIT_INODE=1' '-D_LARGEFILE_SOURCE' '-D_FILE_OFFSET_BITS=64' '-DOPENSSL_NO_PINSHARED' '-DOPENSSL_THREADS' '-DBUILDING_NODE_EXTENSION' '-DNDEBUG' -I/Users/<USER>/Library/Caches/node-gyp/16.13.0/include/node -I/Users/<USER>/Library/Caches/node-gyp/16.13.0/src -I/Users/<USER>/Library/Caches/node-gyp/16.13.0/deps/openssl/config -I/Users/<USER>/Library/Caches/node-gyp/16.13.0/deps/openssl/openssl/include -I/Users/<USER>/Library/Caches/node-gyp/16.13.0/deps/uv/include -I/Users/<USER>/Library/Caches/node-gyp/16.13.0/deps/zlib -I/Users/<USER>/Library/Caches/node-gyp/16.13.0/deps/v8/include -I./Release/obj/gen/sqlite3  -O3 -mmacosx-version-min=10.7 -arch x86_64 -Wall -Wendif-labels -W -Wno-unused-parameter -std=gnu++14 -stdlib=libc++ -fno-rtti -fno-exceptions -fvisibility-inlines-hidden -std=c++20 -stdlib=libc++ -MMD -MF ./Release/.deps/Release/obj.target/better_sqlite3/src/better_sqlite3.o.d.raw   -c
Release/obj.target/better_sqlite3/src/better_sqlite3.o: \
  ../src/better_sqlite3.cpp ../src/better_sqlite3.hpp \
  Release/obj/gen/sqlite3/sqlite3.h \
  /Users/<USER>/Library/Caches/node-gyp/16.13.0/include/node/node.h \
  /Users/<USER>/Library/Caches/node-gyp/16.13.0/include/node/v8.h \
  /Users/<USER>/Library/Caches/node-gyp/16.13.0/include/node/cppgc/common.h \
  /Users/<USER>/Library/Caches/node-gyp/16.13.0/include/node/v8config.h \
  /Users/<USER>/Library/Caches/node-gyp/16.13.0/include/node/v8-internal.h \
  /Users/<USER>/Library/Caches/node-gyp/16.13.0/include/node/v8-version.h \
  /Users/<USER>/Library/Caches/node-gyp/16.13.0/include/node/v8-platform.h \
  /Users/<USER>/Library/Caches/node-gyp/16.13.0/include/node/node_version.h \
  /Users/<USER>/Library/Caches/node-gyp/16.13.0/include/node/node_object_wrap.h \
  /Users/<USER>/Library/Caches/node-gyp/16.13.0/include/node/node_buffer.h
../src/better_sqlite3.cpp:
../src/better_sqlite3.hpp:
Release/obj/gen/sqlite3/sqlite3.h:
/Users/<USER>/Library/Caches/node-gyp/16.13.0/include/node/node.h:
/Users/<USER>/Library/Caches/node-gyp/16.13.0/include/node/v8.h:
/Users/<USER>/Library/Caches/node-gyp/16.13.0/include/node/cppgc/common.h:
/Users/<USER>/Library/Caches/node-gyp/16.13.0/include/node/v8config.h:
/Users/<USER>/Library/Caches/node-gyp/16.13.0/include/node/v8-internal.h:
/Users/<USER>/Library/Caches/node-gyp/16.13.0/include/node/v8-version.h:
/Users/<USER>/Library/Caches/node-gyp/16.13.0/include/node/v8-platform.h:
/Users/<USER>/Library/Caches/node-gyp/16.13.0/include/node/node_version.h:
/Users/<USER>/Library/Caches/node-gyp/16.13.0/include/node/node_object_wrap.h:
/Users/<USER>/Library/Caches/node-gyp/16.13.0/include/node/node_buffer.h:
