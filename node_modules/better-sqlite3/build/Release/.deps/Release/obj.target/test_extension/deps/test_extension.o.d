cmd_Release/obj.target/test_extension/deps/test_extension.o := cc -o Release/obj.target/test_extension/deps/test_extension.o ../deps/test_extension.c '-DNODE_GYP_MODULE_NAME=test_extension' '-DUSING_UV_SHARED=1' '-DUSING_V8_SHARED=1' '-DV8_DEPRECATION_WARNINGS=1' '-DV8_DEPRECATION_WARNINGS' '-DV8_IMMINENT_DEPRECATION_WARNINGS' '-D_GLIBCXX_USE_CXX11_ABI=1' '-D_DARWIN_USE_64_BIT_INODE=1' '-D_LARGEFILE_SOURCE' '-D_FILE_OFFSET_BITS=64' '-DOPENSSL_NO_PINSHARED' '-DOPENSSL_THREADS' '-DBUILDING_NODE_EXTENSION' '-DNDEBUG' -I/Users/<USER>/Library/Caches/node-gyp/16.13.0/include/node -I/Users/<USER>/Library/Caches/node-gyp/16.13.0/src -I/Users/<USER>/Library/Caches/node-gyp/16.13.0/deps/openssl/config -I/Users/<USER>/Library/Caches/node-gyp/16.13.0/deps/openssl/openssl/include -I/Users/<USER>/Library/Caches/node-gyp/16.13.0/deps/uv/include -I/Users/<USER>/Library/Caches/node-gyp/16.13.0/deps/zlib -I/Users/<USER>/Library/Caches/node-gyp/16.13.0/deps/v8/include -I./Release/obj/gen/sqlite3  -O3 -mmacosx-version-min=10.7 -arch x86_64 -Wall -Wendif-labels -W -Wno-unused-parameter -fno-strict-aliasing -MMD -MF ./Release/.deps/Release/obj.target/test_extension/deps/test_extension.o.d.raw   -c
Release/obj.target/test_extension/deps/test_extension.o: \
  ../deps/test_extension.c Release/obj/gen/sqlite3/sqlite3ext.h \
  Release/obj/gen/sqlite3/sqlite3.h
../deps/test_extension.c:
Release/obj/gen/sqlite3/sqlite3ext.h:
Release/obj/gen/sqlite3/sqlite3.h:
