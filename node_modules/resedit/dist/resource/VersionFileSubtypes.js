"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.VersionFileFontSubtype = exports.VersionFileDriverSubtype = void 0;
var VersionFileDriverSubtype;
(function (VersionFileDriverSubtype) {
    VersionFileDriverSubtype[VersionFileDriverSubtype["Unknown"] = 0] = "Unknown";
    VersionFileDriverSubtype[VersionFileDriverSubtype["Printer"] = 1] = "Printer";
    VersionFileDriverSubtype[VersionFileDriverSubtype["Keyboard"] = 2] = "Keyboard";
    VersionFileDriverSubtype[VersionFileDriverSubtype["Language"] = 3] = "Language";
    VersionFileDriverSubtype[VersionFileDriverSubtype["Display"] = 4] = "Display";
    VersionFileDriverSubtype[VersionFileDriverSubtype["Mouse"] = 5] = "Mouse";
    VersionFileDriverSubtype[VersionFileDriverSubtype["Network"] = 6] = "Network";
    VersionFileDriverSubtype[VersionFileDriverSubtype["System"] = 7] = "System";
    VersionFileDriverSubtype[VersionFileDriverSubtype["Installable"] = 8] = "Installable";
    VersionFileDriverSubtype[VersionFileDriverSubtype["Sound"] = 9] = "Sound";
    VersionFileDriverSubtype[VersionFileDriverSubtype["Comm"] = 10] = "Comm";
    VersionFileDriverSubtype[VersionFileDriverSubtype["VersionedPrinter"] = 12] = "VersionedPrinter";
})(VersionFileDriverSubtype = exports.VersionFileDriverSubtype || (exports.VersionFileDriverSubtype = {}));
var VersionFileFontSubtype;
(function (VersionFileFontSubtype) {
    VersionFileFontSubtype[VersionFileFontSubtype["Unknown"] = 0] = "Unknown";
    VersionFileFontSubtype[VersionFileFontSubtype["Raster"] = 1] = "Raster";
    VersionFileFontSubtype[VersionFileFontSubtype["Vector"] = 2] = "Vector";
    VersionFileFontSubtype[VersionFileFontSubtype["TrueType"] = 3] = "TrueType";
})(VersionFileFontSubtype = exports.VersionFileFontSubtype || (exports.VersionFileFontSubtype = {}));
