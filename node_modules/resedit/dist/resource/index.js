"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.VersionInfo = exports.VersionFileType = exports.VersionFileFontSubtype = exports.VersionFileDriverSubtype = exports.VersionFileOS = exports.VersionFileFlags = exports.StringTable = exports.IconGroupEntry = void 0;
var IconGroupEntry_js_1 = require("./IconGroupEntry.js");
exports.IconGroupEntry = IconGroupEntry_js_1.default;
var StringTable_js_1 = require("./StringTable.js");
exports.StringTable = StringTable_js_1.default;
var VersionFileFlags_js_1 = require("./VersionFileFlags.js");
exports.VersionFileFlags = VersionFileFlags_js_1.default;
var VersionFileOS_js_1 = require("./VersionFileOS.js");
exports.VersionFileOS = VersionFileOS_js_1.default;
var VersionFileSubtypes_js_1 = require("./VersionFileSubtypes.js");
Object.defineProperty(exports, "VersionFileDriverSubtype", { enumerable: true, get: function () { return VersionFileSubtypes_js_1.VersionFileDriverSubtype; } });
Object.defineProperty(exports, "VersionFileFontSubtype", { enumerable: true, get: function () { return VersionFileSubtypes_js_1.VersionFileFontSubtype; } });
var VersionFileType_js_1 = require("./VersionFileType.js");
exports.VersionFileType = VersionFileType_js_1.default;
var VersionInfo_js_1 = require("./VersionInfo.js");
exports.VersionInfo = VersionInfo_js_1.default;
