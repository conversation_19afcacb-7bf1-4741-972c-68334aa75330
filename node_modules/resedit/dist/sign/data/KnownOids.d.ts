import ObjectIdentifier from './ObjectIdentifier.js';
export declare const OID_SHA1_NO_SIGN: ObjectIdentifier;
export declare const OID_SHA256_NO_SIGN: ObjectIdentifier;
export declare const OID_SHA384_NO_SIGN: ObjectIdentifier;
export declare const OID_SHA512_NO_SIGN: ObjectIdentifier;
export declare const OID_SHA224_NO_SIGN: ObjectIdentifier;
export declare const OID_SHA512_224_NO_SIGN: ObjectIdentifier;
export declare const OID_SHA512_256_NO_SIGN: ObjectIdentifier;
export declare const OID_SHA3_224_NO_SIGN: ObjectIdentifier;
export declare const OID_SHA3_256_NO_SIGN: ObjectIdentifier;
export declare const OID_SHA3_384_NO_SIGN: ObjectIdentifier;
export declare const OID_SHA3_512_NO_SIGN: ObjectIdentifier;
export declare const OID_SHAKE128_NO_SIGN: ObjectIdentifier;
export declare const OID_SHAKE256_NO_SIGN: ObjectIdentifier;
export declare const OID_RSA: ObjectIdentifier;
export declare const OID_DSA: ObjectIdentifier;
export declare const OID_SIGNED_DATA: ObjectIdentifier;
export declare const OID_CONTENT_TYPE: ObjectIdentifier;
export declare const OID_MESSAGE_DIGEST: ObjectIdentifier;
export declare const OID_SPC_STATEMENT_TYPE_OBJID: ObjectIdentifier;
export declare const OID_SPC_SP_OPUS_INFO_OBJID: ObjectIdentifier;
export declare const OID_SPC_INDIVIDUAL_SP_KEY_PURPOSE_OBJID: ObjectIdentifier;
export declare const OID_RFC3161_COUNTER_SIGNATURE: ObjectIdentifier;
