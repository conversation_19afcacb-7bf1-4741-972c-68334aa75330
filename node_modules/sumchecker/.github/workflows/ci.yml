name: CI

on:
  push:
    branches:
      - master
    tags:
      - v[0-9]+.[0-9]+.[0-9]+*
  pull_request:

jobs:
  test:
    runs-on: ${{ matrix.os }}
    strategy:
      matrix:
        os: [windows-latest, macOS-latest, ubuntu-latest]
        node-version: [8.x, 10.x, 12.x]

    steps:
      - name: Fix git checkout line endings
        run: git config --global core.autocrlf input
      - uses: actions/checkout@v1
      - name: Setup Node.js
        uses: actions/setup-node@v1
        with:
          node-version: ${{ matrix.node-version }}
      - name: Cache node_modules
        uses: actions/cache@v1
        with:
          path: node_modules
          key: ${{ runner.OS }}-build-${{ hashFiles('**/package.json') }}
          restore-keys: |
            ${{ runner.OS }}-build-${{ env.cache-name }}-
            ${{ runner.OS }}-build-
            ${{ runner.OS }}-
      - name: Install
        run: npm install
      - name: Lint
        run: npm run lint
      - name: Testsuite
        run: npm run coverage
      - name: Test TypeScript definition
        run: npm run tsd
      - name: Codecov
        run: npm run codecov
        env:
          CODECOV_TOKEN: ${{ secrets.CODECOV_TOKEN }}
