{"name": "temp", "description": "Temporary files and directories", "tags": ["temporary", "temp", "tempfile", "tempdir", "tmpfile", "tmpdir", "security"], "version": "0.9.4", "author": "<PERSON> <<EMAIL>>", "license": "MIT", "directories": {"lib": "lib"}, "engines": {"node": ">=6.0.0"}, "main": "./lib/temp", "dependencies": {"rimraf": "~2.6.2", "mkdirp": "^0.5.1"}, "keywords": ["temporary", "tmp", "temp", "tempdir", "tempfile", "tmpdir", "tmpfile"], "files": ["lib"], "devDependencies": {"mocha": "6.2.3"}, "repository": {"type": "git", "url": "git://github.com/bruce/node-temp.git"}, "scripts": {"test": "mocha test/temp-test.js"}}