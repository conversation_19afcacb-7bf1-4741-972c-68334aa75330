{"name": "yauzl", "version": "2.10.0", "description": "yet another unzip library for node", "main": "index.js", "scripts": {"test": "node test/test.js", "test-cov": "istanbul cover test/test.js", "test-travis": "istanbul cover --report lcovonly test/test.js"}, "repository": {"type": "git", "url": "https://github.com/thejoshwolfe/yauzl.git"}, "keywords": ["unzip", "zip", "stream", "archive", "file"], "author": "<PERSON> <the<PERSON><PERSON><PERSON><PERSON>@gmail.com>", "license": "MIT", "bugs": {"url": "https://github.com/thejoshwolfe/yauzl/issues"}, "homepage": "https://github.com/thejoshwolfe/yauzl", "dependencies": {"fd-slicer": "~1.1.0", "buffer-crc32": "~0.2.3"}, "devDependencies": {"bl": "~1.0.0", "istanbul": "~0.3.4", "pend": "~1.2.0"}, "files": ["index.js"]}