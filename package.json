{"name": "tini-desktop-app", "version": "1.0.0", "description": "A desktop application built with Bun, Electron, and DaisyUI", "main": "src/main/main.js", "type": "module", "private": true, "scripts": {"dev": "concurrently \"npm run dev:backend\" \"npm run dev:electron\"", "dev:backend": "bun run --watch src/backend/server.ts", "dev:electron": "electron src/main/main.js --dev", "dev:frontend": "npm run build:css:watch", "build": "npm run build:clean && npm run build:css && npm run build:electron", "build:clean": "<PERSON><PERSON><PERSON> dist build", "build:css": "tailwindcss -i src/renderer/input.css -o src/renderer/style.css --minify", "build:css:watch": "tailwindcss -i src/renderer/input.css -o src/renderer/style.css --watch", "build:electron": "electron-builder", "build:electron:dir": "electron-builder --dir", "build:backend": "bun build src/backend/server.ts --outdir dist/backend --target bun", "package": "npm run build && npm run package:electron", "package:electron": "electron-builder --publish=never", "package:all": "electron-builder -mwl", "package:mac": "electron-builder --mac", "package:win": "electron-builder --win", "package:linux": "electron-builder --linux", "dist": "npm run build && electron-builder --publish=always", "dist:mac": "npm run build && electron-builder --mac --publish=always", "dist:win": "npm run build && electron-builder --win --publish=always", "dist:linux": "npm run build && electron-builder --linux --publish=always", "test": "npm run test:backend && npm run test:frontend", "test:backend": "bun test src/backend/tests/", "test:frontend": "echo \"Frontend tests not implemented yet\"", "test:auth": "node scripts/test-auth.js", "test:better-auth": "./scripts/test-better-auth.sh", "test:csp": "node scripts/test-csp-fix.js", "test:ui": "node scripts/test-ui-optimization.js", "test:e2e": "echo \"E2E tests not implemented yet\"", "init:auth": "node scripts/init-better-auth.js", "lint": "eslint src/ --ext .js,.ts", "lint:fix": "eslint src/ --ext .js,.ts --fix", "format": "prettier --write src/", "start": "npm run build && electron dist/main/main.js", "start:prod": "NODE_ENV=production npm run start", "postinstall": "electron-builder install-app-deps"}, "keywords": ["electron", "bun", "daisyui", "desktop", "app"], "author": "Your Name", "license": "MIT", "dependencies": {"better-auth": "^1.0.0"}, "devDependencies": {"@types/bun": "latest", "autoprefixer": "^10.4.21", "concurrently": "^8.2.2", "daisyui": "^5.0.43", "electron": "^36.3.2", "electron-builder": "^24.13.3", "eslint": "^8.57.1", "postcss": "^8.5.4", "prettier": "^3.5.3", "rimraf": "^5.0.10", "tailwindcss": "^4.1.8"}, "peerDependencies": {"typescript": "^5"}, "build": {"appId": "com.tini.desktop-app", "productName": "Tini Desktop App", "directories": {"output": "dist", "buildResources": "build"}, "files": ["src/**/*", "node_modules/**/*", "package.json"], "extraResources": [{"from": "assets", "to": "assets", "filter": ["**/*"]}], "mac": {"category": "public.app-category.productivity", "target": [{"target": "dmg", "arch": ["x64", "arm64"]}]}, "win": {"target": [{"target": "nsis", "arch": ["x64"]}]}, "linux": {"target": [{"target": "AppImage", "arch": ["x64"]}]}, "nsis": {"oneClick": false, "allowToChangeInstallationDirectory": true}}}