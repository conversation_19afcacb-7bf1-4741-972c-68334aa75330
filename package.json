{"name": "tini-desktop-app", "version": "1.0.0", "description": "A desktop application built with Bun, Electron, and DaisyUI", "main": "src/main/main.js", "type": "module", "private": true, "scripts": {"dev": "bun run dev:backend & bun run dev:frontend", "dev:backend": "bun run --watch src/backend/server.ts", "dev:frontend": "bun run build:css && electron src/main/main.js", "build": "bun run build:css && bun run build:backend", "build:css": "tailwindcss -i src/renderer/input.css -o src/renderer/style.css --watch", "build:backend": "bun build src/backend/server.ts --outdir dist/backend", "electron": "electron src/main/main.js", "electron:dev": "electron src/main/main.js --dev", "package": "electron-builder", "test": "bun test"}, "keywords": ["electron", "bun", "daisyui", "desktop", "app"], "author": "Your Name", "license": "MIT", "devDependencies": {"@types/bun": "latest", "autoprefixer": "^10.4.21", "daisyui": "^5.0.43", "electron": "^36.3.2", "postcss": "^8.5.4", "tailwindcss": "^4.1.8"}, "peerDependencies": {"typescript": "^5"}}