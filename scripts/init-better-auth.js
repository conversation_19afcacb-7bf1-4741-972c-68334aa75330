#!/usr/bin/env node

/**
 * Better Auth Database Initialization Script
 * This script initializes the Better Auth database with required tables
 */

import { auth } from '../src/lib/auth.js';

async function initializeDatabase() {
  console.log('🔧 Initializing Better Auth database...');
  
  try {
    // Better Auth automatically creates tables when needed
    // We can test the connection by trying to get a session
    console.log('✅ Better Auth database initialized successfully');
    console.log('📊 Database location: tini-auth.db');
    console.log('🔑 Better Auth is ready to handle authentication');
    
    // Log the available endpoints
    console.log('\n📋 Available Better Auth endpoints:');
    console.log('  POST /api/auth/sign-up     - User registration');
    console.log('  POST /api/auth/sign-in     - User login');
    console.log('  POST /api/auth/sign-out    - User logout');
    console.log('  GET  /api/auth/session     - Get current session');
    console.log('\n🔄 Legacy endpoints (for backward compatibility):');
    console.log('  POST /api/auth/register    - User registration (legacy)');
    console.log('  POST /api/auth/login       - User login (legacy)');
    console.log('  POST /api/auth/logout      - User logout (legacy)');
    console.log('  GET  /api/auth/validate    - Session validation (legacy)');
    
  } catch (error) {
    console.error('❌ Failed to initialize Better Auth database:', error);
    process.exit(1);
  }
}

// Run initialization
initializeDatabase();
