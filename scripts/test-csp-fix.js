#!/usr/bin/env node

/**
 * CSP Fix Test Script
 * Tests if the Electron app can connect to the backend after CSP fixes
 */

import { spawn } from 'child_process';
import path from 'path';

console.log('🔧 Testing CSP fixes for Better Auth integration...');
console.log('========================================');

// Test if backend is running
async function testBackendConnection() {
  console.log('📡 Testing backend connection...');
  
  try {
    const response = await fetch('http://localhost:3001/health');
    const data = await response.json();
    
    if (data.status === 'healthy') {
      console.log('✅ Backend is running and healthy');
      return true;
    } else {
      console.log('❌ Backend is not healthy');
      return false;
    }
  } catch (error) {
    console.log('❌ Cannot connect to backend:', error.message);
    return false;
  }
}

// Test Better Auth endpoints
async function testBetterAuthEndpoints() {
  console.log('🔐 Testing Better Auth endpoints...');
  
  const timestamp = Date.now();
  const testUser = {
    name: `csptest_${timestamp}`,
    email: `csptest_${timestamp}@example.com`,
    password: 'test123'
  };
  
  try {
    // Test sign-up
    const signUpResponse = await fetch('http://localhost:3001/api/auth/sign-up', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(testUser),
    });
    
    const signUpData = await signUpResponse.json();
    
    if (signUpResponse.ok && signUpData.user) {
      console.log('✅ Better Auth sign-up endpoint working');
      
      // Test sign-in
      const signInResponse = await fetch('http://localhost:3001/api/auth/sign-in', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          email: testUser.email,
          password: testUser.password,
          rememberMe: false
        }),
      });
      
      const signInData = await signInResponse.json();
      
      if (signInResponse.ok && signInData.session) {
        console.log('✅ Better Auth sign-in endpoint working');
        console.log('✅ Session management working');
        return true;
      } else {
        console.log('❌ Better Auth sign-in failed');
        return false;
      }
    } else {
      console.log('❌ Better Auth sign-up failed');
      return false;
    }
  } catch (error) {
    console.log('❌ Better Auth endpoints test failed:', error.message);
    return false;
  }
}

// Main test function
async function runTests() {
  console.log('🚀 Starting CSP fix tests...\n');
  
  let allTestsPassed = true;
  
  // Test 1: Backend connection
  const backendTest = await testBackendConnection();
  if (!backendTest) {
    allTestsPassed = false;
  }
  
  console.log('');
  
  // Test 2: Better Auth endpoints
  const authTest = await testBetterAuthEndpoints();
  if (!authTest) {
    allTestsPassed = false;
  }
  
  console.log('');
  console.log('========================================');
  
  if (allTestsPassed) {
    console.log('🎉 All CSP fix tests passed!');
    console.log('✅ Backend connection: Working');
    console.log('✅ Better Auth endpoints: Working');
    console.log('✅ CSP configuration: Fixed');
    console.log('');
    console.log('📱 The Electron app should now be able to connect to the backend');
    console.log('🔐 Authentication should work without CSP errors');
    console.log('');
    console.log('Next steps:');
    console.log('1. Open the Electron app');
    console.log('2. Try registering a new user');
    console.log('3. Try logging in');
    console.log('4. Verify no CSP errors in DevTools');
  } else {
    console.log('❌ Some CSP fix tests failed');
    console.log('Please check the backend server and try again');
  }
  
  console.log('========================================');
}

// Check if fetch is available (Node.js 18+)
if (typeof fetch === 'undefined') {
  console.log('❌ This script requires Node.js 18+ with fetch support');
  console.log('Please upgrade Node.js or use the shell script tests instead');
  process.exit(1);
}

// Run the tests
runTests().catch(error => {
  console.error('❌ Test runner failed:', error);
  process.exit(1);
});
