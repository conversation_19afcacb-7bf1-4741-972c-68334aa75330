// Bun backend server for the desktop application
import { serve } from "bun";

// Server configuration
const PORT = process.env.PORT || 3001;
const HOST = process.env.HOST || "localhost";

// In-memory data store (in a real app, you'd use a database)
interface AppData {
  settings: Record<string, any>;
  userPreferences: Record<string, any>;
  logs: Array<{ timestamp: string; level: string; message: string }>;
}

const appData: AppData = {
  settings: {
    theme: "light",
    autoSave: true,
    notifications: true,
  },
  userPreferences: {
    language: "en",
    timezone: "UTC",
  },
  logs: [],
};

// Utility functions
function log(level: string, message: string) {
  const timestamp = new Date().toISOString();
  const logEntry = { timestamp, level, message };
  appData.logs.push(logEntry);
  console.log(`[${timestamp}] ${level.toUpperCase()}: ${message}`);
  
  // Keep only last 1000 log entries
  if (appData.logs.length > 1000) {
    appData.logs.shift();
  }
}

function createResponse(data: any, status = 200) {
  return new Response(JSON.stringify(data), {
    status,
    headers: {
      "Content-Type": "application/json",
      "Access-Control-Allow-Origin": "*",
      "Access-Control-Allow-Methods": "GET, POST, PUT, DELETE, OPTIONS",
      "Access-Control-Allow-Headers": "Content-Type, Authorization",
    },
  });
}

function createErrorResponse(message: string, status = 400) {
  return createResponse({ error: message, success: false }, status);
}

// API Routes
const routes = {
  // Health check
  "GET /health": () => {
    return createResponse({
      status: "healthy",
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
      version: "1.0.0",
    });
  },

  // Get application settings
  "GET /api/settings": () => {
    log("info", "Settings requested");
    return createResponse({
      success: true,
      data: appData.settings,
    });
  },

  // Update application settings
  "POST /api/settings": async (request: Request) => {
    try {
      const body = await request.json();
      appData.settings = { ...appData.settings, ...body };
      log("info", `Settings updated: ${JSON.stringify(body)}`);
      
      return createResponse({
        success: true,
        message: "Settings updated successfully",
        data: appData.settings,
      });
    } catch (error) {
      log("error", `Failed to update settings: ${error}`);
      return createErrorResponse("Invalid JSON in request body");
    }
  },

  // Get user preferences
  "GET /api/preferences": () => {
    log("info", "User preferences requested");
    return createResponse({
      success: true,
      data: appData.userPreferences,
    });
  },

  // Update user preferences
  "POST /api/preferences": async (request: Request) => {
    try {
      const body = await request.json();
      appData.userPreferences = { ...appData.userPreferences, ...body };
      log("info", `User preferences updated: ${JSON.stringify(body)}`);
      
      return createResponse({
        success: true,
        message: "Preferences updated successfully",
        data: appData.userPreferences,
      });
    } catch (error) {
      log("error", `Failed to update preferences: ${error}`);
      return createErrorResponse("Invalid JSON in request body");
    }
  },

  // Get application logs
  "GET /api/logs": () => {
    const limit = 100; // Return last 100 logs
    const recentLogs = appData.logs.slice(-limit);
    
    return createResponse({
      success: true,
      data: recentLogs,
      total: appData.logs.length,
    });
  },

  // System information
  "GET /api/system": () => {
    return createResponse({
      success: true,
      data: {
        platform: process.platform,
        arch: process.arch,
        nodeVersion: process.version,
        bunVersion: Bun.version,
        memory: process.memoryUsage(),
        uptime: process.uptime(),
      },
    });
  },

  // Test endpoint for frontend
  "GET /api/test": () => {
    log("info", "Test endpoint called");
    return createResponse({
      success: true,
      message: "Backend is working correctly!",
      timestamp: new Date().toISOString(),
    });
  },

  // File operations (placeholder)
  "POST /api/files/save": async (request: Request) => {
    try {
      const body = await request.json();
      const { filename, content } = body;
      
      if (!filename || content === undefined) {
        return createErrorResponse("Filename and content are required");
      }
      
      // In a real app, you would save to filesystem
      log("info", `File save requested: ${filename}`);
      
      return createResponse({
        success: true,
        message: `File ${filename} saved successfully`,
        filename,
        size: content.length,
      });
    } catch (error) {
      log("error", `File save failed: ${error}`);
      return createErrorResponse("Failed to save file");
    }
  },

  "GET /api/files/list": () => {
    // In a real app, you would list actual files
    const mockFiles = [
      { name: "document1.txt", size: 1024, modified: new Date().toISOString() },
      { name: "document2.txt", size: 2048, modified: new Date().toISOString() },
    ];
    
    return createResponse({
      success: true,
      data: mockFiles,
    });
  },
};

// Request handler
async function handleRequest(request: Request): Promise<Response> {
  const url = new URL(request.url);
  const method = request.method;
  const path = url.pathname;
  const routeKey = `${method} ${path}`;

  log("info", `${method} ${path}`);

  // Handle CORS preflight
  if (method === "OPTIONS") {
    return new Response(null, {
      status: 200,
      headers: {
        "Access-Control-Allow-Origin": "*",
        "Access-Control-Allow-Methods": "GET, POST, PUT, DELETE, OPTIONS",
        "Access-Control-Allow-Headers": "Content-Type, Authorization",
      },
    });
  }

  // Find matching route
  const handler = routes[routeKey as keyof typeof routes];
  
  if (handler) {
    try {
      return await handler(request);
    } catch (error) {
      log("error", `Route handler error: ${error}`);
      return createErrorResponse("Internal server error", 500);
    }
  }

  // 404 for unknown routes
  log("warn", `Route not found: ${routeKey}`);
  return createErrorResponse("Route not found", 404);
}

// Start the server
const server = serve({
  port: PORT,
  hostname: HOST,
  fetch: handleRequest,
});

log("info", `🚀 Bun server started on http://${HOST}:${PORT}`);
log("info", `📊 Health check available at http://${HOST}:${PORT}/health`);
log("info", `🔧 API endpoints available at http://${HOST}:${PORT}/api/*`);

// Graceful shutdown
process.on("SIGINT", () => {
  log("info", "Received SIGINT, shutting down gracefully");
  server.stop();
  process.exit(0);
});

process.on("SIGTERM", () => {
  log("info", "Received SIGTERM, shutting down gracefully");
  server.stop();
  process.exit(0);
});

export default server;
