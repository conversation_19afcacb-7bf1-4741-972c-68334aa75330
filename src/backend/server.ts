// Bun backend server for the desktop application with Better Auth integration
import { serve } from "bun";
import { auth } from "../lib/auth";

// Server configuration
const PORT = process.env.PORT || 3001;
const HOST = process.env.HOST || "localhost";

// User interface for authentication
interface User {
  id: string;
  username: string;
  email: string;
  passwordHash: string;
  createdAt: string;
  lastLogin?: string;
}

// Session interface
interface Session {
  id: string;
  userId: string;
  createdAt: string;
  expiresAt: string;
  rememberMe: boolean;
}

// Rate limiting interface
interface RateLimit {
  ip: string;
  attempts: number;
  lastAttempt: string;
  blockedUntil?: string;
}

// In-memory data store (in a real app, you'd use a database)
interface AppData {
  settings: Record<string, any>;
  userPreferences: Record<string, any>;
  logs: Array<{ timestamp: string; level: string; message: string }>;
  users: Map<string, User>;
  sessions: Map<string, Session>;
  rateLimits: Map<string, RateLimit>;
}

const appData: AppData = {
  settings: {
    theme: "light",
    autoSave: true,
    notifications: true,
  },
  userPreferences: {
    language: "en",
    timezone: "UTC",
  },
  logs: [],
  users: new Map(),
  sessions: new Map(),
  rateLimits: new Map(),
};

// Authentication utility functions
async function hashPassword(password: string): Promise<string> {
  const hasher = new Bun.CryptoHasher("sha256");
  hasher.update(password + "tini_salt_2024"); // Simple salt for demo
  return hasher.digest("hex");
}

async function verifyPassword(password: string, hash: string): Promise<boolean> {
  const passwordHash = await hashPassword(password);
  return passwordHash === hash;
}

function generateId(): string {
  return Math.random().toString(36).substring(2) + Date.now().toString(36);
}

function generateSessionId(): string {
  return generateId() + "_session";
}

function isValidEmail(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
}

function isValidPassword(password: string): boolean {
  // At least 6 characters, contains letter and number
  return password.length >= 6 && /[a-zA-Z]/.test(password) && /[0-9]/.test(password);
}

function sanitizeInput(input: string): string {
  return input.trim().replace(/[<>]/g, "");
}

function checkRateLimit(ip: string): { allowed: boolean; remainingTime?: number } {
  const now = new Date();
  const rateLimit = appData.rateLimits.get(ip);

  if (!rateLimit) {
    return { allowed: true };
  }

  // Check if still blocked
  if (rateLimit.blockedUntil && new Date(rateLimit.blockedUntil) > now) {
    const remainingTime = Math.ceil((new Date(rateLimit.blockedUntil).getTime() - now.getTime()) / 1000);
    return { allowed: false, remainingTime };
  }

  // Reset if more than 15 minutes since last attempt
  const lastAttempt = new Date(rateLimit.lastAttempt);
  if (now.getTime() - lastAttempt.getTime() > 15 * 60 * 1000) {
    appData.rateLimits.delete(ip);
    return { allowed: true };
  }

  // Check if too many attempts
  if (rateLimit.attempts >= 5) {
    rateLimit.blockedUntil = new Date(now.getTime() + 15 * 60 * 1000).toISOString(); // Block for 15 minutes
    return { allowed: false, remainingTime: 15 * 60 };
  }

  return { allowed: true };
}

function recordFailedAttempt(ip: string) {
  const now = new Date().toISOString();
  const existing = appData.rateLimits.get(ip);

  if (existing) {
    existing.attempts += 1;
    existing.lastAttempt = now;
  } else {
    appData.rateLimits.set(ip, {
      ip,
      attempts: 1,
      lastAttempt: now,
    });
  }
}

function createSession(userId: string, rememberMe: boolean = false): Session {
  const sessionId = generateSessionId();
  const now = new Date();
  const expiresAt = new Date(now.getTime() + (rememberMe ? 30 * 24 * 60 * 60 * 1000 : 24 * 60 * 60 * 1000)); // 30 days or 1 day

  const session: Session = {
    id: sessionId,
    userId,
    createdAt: now.toISOString(),
    expiresAt: expiresAt.toISOString(),
    rememberMe,
  };

  appData.sessions.set(sessionId, session);
  return session;
}

function validateSession(sessionId: string): { valid: boolean; userId?: string } {
  const session = appData.sessions.get(sessionId);

  if (!session) {
    return { valid: false };
  }

  if (new Date(session.expiresAt) < new Date()) {
    appData.sessions.delete(sessionId);
    return { valid: false };
  }

  return { valid: true, userId: session.userId };
}

function getClientIP(request: Request): string {
  return request.headers.get("x-forwarded-for") ||
         request.headers.get("x-real-ip") ||
         "127.0.0.1";
}

// Utility functions
function log(level: string, message: string) {
  const timestamp = new Date().toISOString();
  const logEntry = { timestamp, level, message };
  appData.logs.push(logEntry);
  console.log(`[${timestamp}] ${level.toUpperCase()}: ${message}`);

  // Keep only last 1000 log entries
  if (appData.logs.length > 1000) {
    appData.logs.shift();
  }
}

function createResponse(data: any, status = 200) {
  return new Response(JSON.stringify(data), {
    status,
    headers: {
      "Content-Type": "application/json",
      "Access-Control-Allow-Origin": "*",
      "Access-Control-Allow-Methods": "GET, POST, PUT, DELETE, OPTIONS",
      "Access-Control-Allow-Headers": "Content-Type, Authorization",
    },
  });
}

function createErrorResponse(message: string, status = 400) {
  return createResponse({ error: message, success: false }, status);
}

// API Routes
const routes = {
  // Health check
  "GET /health": () => {
    return createResponse({
      status: "healthy",
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
      version: "1.0.0",
    });
  },

  // Better Auth-style endpoints
  "POST /api/auth/sign-up": async (request: Request) => {
    try {
      const clientIP = getClientIP(request);
      const rateCheck = checkRateLimit(clientIP);

      if (!rateCheck.allowed) {
        return createErrorResponse(
          `Too many attempts. Try again in ${rateCheck.remainingTime} seconds.`,
          429
        );
      }

      const body = await request.json();
      const { email, password, name } = body;

      // Validate input
      if (!name || !email || !password) {
        recordFailedAttempt(clientIP);
        return createErrorResponse("All fields are required");
      }

      // Sanitize inputs
      const cleanUsername = sanitizeInput(name);
      const cleanEmail = sanitizeInput(email).toLowerCase();

      // Validate email format
      if (!isValidEmail(cleanEmail)) {
        recordFailedAttempt(clientIP);
        return createErrorResponse("Invalid email format");
      }

      // Validate password strength
      if (!isValidPassword(password)) {
        recordFailedAttempt(clientIP);
        return createErrorResponse("Password must be at least 6 characters and contain both letters and numbers");
      }

      // Check if user already exists
      const existingUser = Array.from(appData.users.values()).find(
        user => user.email === cleanEmail || user.username === cleanUsername
      );

      if (existingUser) {
        recordFailedAttempt(clientIP);
        return createErrorResponse("User with this email or username already exists");
      }

      // Create new user
      const userId = generateId();
      const passwordHash = await hashPassword(password);

      const newUser: User = {
        id: userId,
        username: cleanUsername,
        email: cleanEmail,
        passwordHash,
        createdAt: new Date().toISOString(),
      };

      appData.users.set(userId, newUser);

      // Create session for auto sign-in
      const session = createSession(userId, false);

      log("info", `New user registered: ${cleanUsername} (${cleanEmail})`);

      return createResponse({
        user: {
          id: userId,
          email: cleanEmail,
          name: cleanUsername,
          createdAt: newUser.createdAt,
        },
        session: {
          id: session.id,
          userId: session.userId,
          expiresAt: session.expiresAt,
        },
      });
    } catch (error) {
      log("error", `Registration error: ${error}`);
      return createErrorResponse("Registration failed", 500);
    }
  },

  "POST /api/auth/sign-in": async (request: Request) => {
    try {
      const clientIP = getClientIP(request);
      const rateCheck = checkRateLimit(clientIP);

      if (!rateCheck.allowed) {
        return createErrorResponse(
          `Too many attempts. Try again in ${rateCheck.remainingTime} seconds.`,
          429
        );
      }

      const body = await request.json();
      const { email, password, rememberMe } = body;

      // Validate input
      if (!email || !password) {
        recordFailedAttempt(clientIP);
        return createErrorResponse("Email and password are required");
      }

      // Sanitize input
      const cleanEmail = sanitizeInput(email).toLowerCase();

      // Find user by email or username
      const user = Array.from(appData.users.values()).find(
        user => user.email === cleanEmail || user.username.toLowerCase() === cleanEmail
      );

      if (!user) {
        recordFailedAttempt(clientIP);
        return createErrorResponse("Invalid credentials");
      }

      // Verify password
      const isValidPassword = await verifyPassword(password, user.passwordHash);
      if (!isValidPassword) {
        recordFailedAttempt(clientIP);
        return createErrorResponse("Invalid credentials");
      }

      // Update last login
      user.lastLogin = new Date().toISOString();

      // Create session
      const session = createSession(user.id, rememberMe);

      log("info", `User logged in: ${user.username}`);

      return createResponse({
        user: {
          id: user.id,
          email: user.email,
          name: user.username,
          createdAt: user.createdAt,
        },
        session: {
          id: session.id,
          userId: session.userId,
          expiresAt: session.expiresAt,
        },
      });
    } catch (error) {
      log("error", `Login error: ${error}`);
      return createErrorResponse("Login failed", 500);
    }
  },

  "POST /api/auth/sign-out": async (request: Request) => {
    try {
      const body = await request.json();
      const { sessionId } = body;

      if (sessionId && appData.sessions.has(sessionId)) {
        const session = appData.sessions.get(sessionId);
        appData.sessions.delete(sessionId);

        if (session) {
          const user = appData.users.get(session.userId);
          log("info", `User logged out: ${user?.username || 'unknown'}`);
        }
      }

      return createResponse({
        success: true,
        message: "Logout successful",
      });
    } catch (error) {
      log("error", `Logout error: ${error}`);
      return createErrorResponse("Logout failed", 500);
    }
  },

  "GET /api/auth/session": async (request: Request) => {
    try {
      const url = new URL(request.url);
      const sessionId = url.searchParams.get("sessionId") ||
                       request.headers.get("authorization")?.replace("Bearer ", "") ||
                       request.headers.get("cookie")?.match(/better-auth\.session_token=([^;]+)/)?.[1];

      if (!sessionId) {
        return createErrorResponse("Session ID required", 401);
      }

      const validation = validateSession(sessionId);

      if (!validation.valid) {
        return createErrorResponse("Invalid or expired session", 401);
      }

      const user = appData.users.get(validation.userId!);
      if (!user) {
        return createErrorResponse("User not found", 401);
      }

      const session = appData.sessions.get(sessionId);

      return createResponse({
        user: {
          id: user.id,
          email: user.email,
          name: user.username,
          createdAt: user.createdAt,
        },
        session: {
          id: sessionId,
          userId: user.id,
          expiresAt: session?.expiresAt,
        },
      });
    } catch (error) {
      log("error", `Session validation error: ${error}`);
      return createErrorResponse("Validation failed", 500);
    }
  },

  // Legacy API endpoints for backward compatibility
  "POST /api/auth/register": async (request: Request) => {
    try {
      const clientIP = getClientIP(request);
      const rateCheck = checkRateLimit(clientIP);

      if (!rateCheck.allowed) {
        return createErrorResponse(
          `Too many attempts. Try again in ${rateCheck.remainingTime} seconds.`,
          429
        );
      }

      const body = await request.json();
      const { username, email, password, confirmPassword } = body;

      // Validate input
      if (!username || !email || !password || !confirmPassword) {
        recordFailedAttempt(clientIP);
        return createErrorResponse("All fields are required");
      }

      if (password !== confirmPassword) {
        recordFailedAttempt(clientIP);
        return createErrorResponse("Passwords do not match");
      }

      // Sanitize inputs
      const cleanUsername = sanitizeInput(username);
      const cleanEmail = sanitizeInput(email).toLowerCase();

      // Validate email format
      if (!isValidEmail(cleanEmail)) {
        recordFailedAttempt(clientIP);
        return createErrorResponse("Invalid email format");
      }

      // Validate password strength
      if (!isValidPassword(password)) {
        recordFailedAttempt(clientIP);
        return createErrorResponse("Password must be at least 6 characters and contain both letters and numbers");
      }

      // Check if user already exists
      const existingUser = Array.from(appData.users.values()).find(
        user => user.email === cleanEmail || user.username === cleanUsername
      );

      if (existingUser) {
        recordFailedAttempt(clientIP);
        return createErrorResponse("User with this email or username already exists");
      }

      // Create new user
      const userId = generateId();
      const passwordHash = await hashPassword(password);

      const newUser: User = {
        id: userId,
        username: cleanUsername,
        email: cleanEmail,
        passwordHash,
        createdAt: new Date().toISOString(),
      };

      appData.users.set(userId, newUser);
      log("info", `New user registered: ${cleanUsername} (${cleanEmail})`);

      return createResponse({
        success: true,
        message: "User registered successfully",
        user: {
          id: userId,
          username: cleanUsername,
          email: cleanEmail,
          createdAt: newUser.createdAt,
        },
      });
    } catch (error) {
      log("error", `Registration error: ${error}`);
      return createErrorResponse("Registration failed", 500);
    }
  },

  "POST /api/auth/login": async (request: Request) => {
    try {
      const clientIP = getClientIP(request);
      const rateCheck = checkRateLimit(clientIP);

      if (!rateCheck.allowed) {
        return createErrorResponse(
          `Too many attempts. Try again in ${rateCheck.remainingTime} seconds.`,
          429
        );
      }

      const body = await request.json();
      const { emailOrUsername, password, rememberMe } = body;

      // Validate input
      if (!emailOrUsername || !password) {
        recordFailedAttempt(clientIP);
        return createErrorResponse("Email/username and password are required");
      }

      // Sanitize input
      const cleanEmailOrUsername = sanitizeInput(emailOrUsername).toLowerCase();

      // Find user by email or username
      const user = Array.from(appData.users.values()).find(
        user => user.email === cleanEmailOrUsername || user.username.toLowerCase() === cleanEmailOrUsername
      );

      if (!user) {
        recordFailedAttempt(clientIP);
        return createErrorResponse("Invalid credentials");
      }

      // Verify password
      const isValidPassword = await verifyPassword(password, user.passwordHash);
      if (!isValidPassword) {
        recordFailedAttempt(clientIP);
        return createErrorResponse("Invalid credentials");
      }

      // Update last login
      user.lastLogin = new Date().toISOString();

      // Create session
      const session = createSession(user.id, rememberMe);

      log("info", `User logged in: ${user.username}`);

      return createResponse({
        success: true,
        message: "Login successful",
        sessionId: session.id,
        user: {
          id: user.id,
          username: user.username,
          email: user.email,
          lastLogin: user.lastLogin,
        },
      });
    } catch (error) {
      log("error", `Login error: ${error}`);
      return createErrorResponse("Login failed", 500);
    }
  },

  "POST /api/auth/logout": async (request: Request) => {
    try {
      const body = await request.json();
      const { sessionId } = body;

      if (sessionId && appData.sessions.has(sessionId)) {
        const session = appData.sessions.get(sessionId);
        appData.sessions.delete(sessionId);

        if (session) {
          const user = appData.users.get(session.userId);
          log("info", `User logged out: ${user?.username || 'unknown'}`);
        }
      }

      return createResponse({
        success: true,
        message: "Logout successful",
      });
    } catch (error) {
      log("error", `Logout error: ${error}`);
      return createErrorResponse("Logout failed", 500);
    }
  },

  "GET /api/auth/validate": async (request: Request) => {
    try {
      const url = new URL(request.url);
      const sessionId = url.searchParams.get("sessionId");

      if (!sessionId) {
        return createErrorResponse("Session ID required", 401);
      }

      const validation = validateSession(sessionId);

      if (!validation.valid) {
        return createErrorResponse("Invalid or expired session", 401);
      }

      const user = appData.users.get(validation.userId!);
      if (!user) {
        return createErrorResponse("User not found", 401);
      }

      return createResponse({
        success: true,
        valid: true,
        user: {
          id: user.id,
          username: user.username,
          email: user.email,
          lastLogin: user.lastLogin,
        },
      });
    } catch (error) {
      log("error", `Session validation error: ${error}`);
      return createErrorResponse("Validation failed", 500);
    }
  },

  // Get application settings
  "GET /api/settings": () => {
    log("info", "Settings requested");
    return createResponse({
      success: true,
      data: appData.settings,
    });
  },

  // Update application settings
  "POST /api/settings": async (request: Request) => {
    try {
      const body = await request.json();
      appData.settings = { ...appData.settings, ...body };
      log("info", `Settings updated: ${JSON.stringify(body)}`);
      
      return createResponse({
        success: true,
        message: "Settings updated successfully",
        data: appData.settings,
      });
    } catch (error) {
      log("error", `Failed to update settings: ${error}`);
      return createErrorResponse("Invalid JSON in request body");
    }
  },

  // Get user preferences
  "GET /api/preferences": () => {
    log("info", "User preferences requested");
    return createResponse({
      success: true,
      data: appData.userPreferences,
    });
  },

  // Update user preferences
  "POST /api/preferences": async (request: Request) => {
    try {
      const body = await request.json();
      appData.userPreferences = { ...appData.userPreferences, ...body };
      log("info", `User preferences updated: ${JSON.stringify(body)}`);
      
      return createResponse({
        success: true,
        message: "Preferences updated successfully",
        data: appData.userPreferences,
      });
    } catch (error) {
      log("error", `Failed to update preferences: ${error}`);
      return createErrorResponse("Invalid JSON in request body");
    }
  },

  // Get application logs
  "GET /api/logs": () => {
    const limit = 100; // Return last 100 logs
    const recentLogs = appData.logs.slice(-limit);
    
    return createResponse({
      success: true,
      data: recentLogs,
      total: appData.logs.length,
    });
  },

  // System information
  "GET /api/system": () => {
    return createResponse({
      success: true,
      data: {
        platform: process.platform,
        arch: process.arch,
        nodeVersion: process.version,
        bunVersion: Bun.version,
        memory: process.memoryUsage(),
        uptime: process.uptime(),
      },
    });
  },

  // Test endpoint for frontend
  "GET /api/test": () => {
    log("info", "Test endpoint called");
    return createResponse({
      success: true,
      message: "Backend is working correctly!",
      timestamp: new Date().toISOString(),
    });
  },

  // File operations (placeholder)
  "POST /api/files/save": async (request: Request) => {
    try {
      const body = await request.json();
      const { filename, content } = body;
      
      if (!filename || content === undefined) {
        return createErrorResponse("Filename and content are required");
      }
      
      // In a real app, you would save to filesystem
      log("info", `File save requested: ${filename}`);
      
      return createResponse({
        success: true,
        message: `File ${filename} saved successfully`,
        filename,
        size: content.length,
      });
    } catch (error) {
      log("error", `File save failed: ${error}`);
      return createErrorResponse("Failed to save file");
    }
  },

  "GET /api/files/list": () => {
    // In a real app, you would list actual files
    const mockFiles = [
      { name: "document1.txt", size: 1024, modified: new Date().toISOString() },
      { name: "document2.txt", size: 2048, modified: new Date().toISOString() },
    ];
    
    return createResponse({
      success: true,
      data: mockFiles,
    });
  },
};

// Request handler
async function handleRequest(request: Request): Promise<Response> {
  const url = new URL(request.url);
  const method = request.method;
  const path = url.pathname;
  const routeKey = `${method} ${path}`;

  log("info", `${method} ${path}`);

  // CORS headers
  const corsHeaders = {
    "Access-Control-Allow-Origin": "*",
    "Access-Control-Allow-Methods": "GET, POST, PUT, DELETE, OPTIONS",
    "Access-Control-Allow-Headers": "Content-Type, Authorization",
  };

  // Handle CORS preflight
  if (method === "OPTIONS") {
    return new Response(null, {
      status: 200,
      headers: corsHeaders,
    });
  }

  // All auth routes are now handled by our custom implementation

  // Find matching route
  const handler = routes[routeKey as keyof typeof routes];

  if (handler) {
    try {
      const response = await handler(request);
      // Add CORS headers to custom route responses
      Object.entries(corsHeaders).forEach(([key, value]) => {
        response.headers.set(key, value);
      });
      return response;
    } catch (error) {
      log("error", `Route handler error: ${error}`);
      return createErrorResponse("Internal server error", 500);
    }
  }

  // 404 for unknown routes
  log("warn", `Route not found: ${routeKey}`);
  return createErrorResponse("Route not found", 404);
}

// Start the server
const server = serve({
  port: PORT,
  hostname: HOST,
  fetch: handleRequest,
});

log("info", `🚀 Bun server started on http://${HOST}:${PORT}`);
log("info", `📊 Health check available at http://${HOST}:${PORT}/health`);
log("info", `🔧 API endpoints available at http://${HOST}:${PORT}/api/*`);

// Graceful shutdown
process.on("SIGINT", () => {
  log("info", "Received SIGINT, shutting down gracefully");
  server.stop();
  process.exit(0);
});

process.on("SIGTERM", () => {
  log("info", "Received SIGTERM, shutting down gracefully");
  server.stop();
  process.exit(0);
});

export default server;
