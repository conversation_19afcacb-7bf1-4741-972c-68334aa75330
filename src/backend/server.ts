// Bun backend server for the desktop application with Better Auth
import { serve } from "bun";
import { auth } from "../lib/auth";

// Server configuration
const PORT = process.env.PORT || 3001;
const HOST = process.env.HOST || "localhost";

// Simplified app data (Better Auth handles users and sessions)
interface AppData {
  settings: Record<string, any>;
  userPreferences: Record<string, any>;
  logs: Array<{ timestamp: string; level: string; message: string }>;
}

const appData: AppData = {
  settings: {
    theme: "light",
    autoSave: true,
    notifications: true,
  },
  userPreferences: {
    language: "en",
    timezone: "UTC",
  },
  logs: [],
};

// Utility functions for Better Auth integration
function getClientIP(request: Request): string {
  return request.headers.get("x-forwarded-for") ||
         request.headers.get("x-real-ip") ||
         "127.0.0.1";
}

// Utility functions
function log(level: string, message: string) {
  const timestamp = new Date().toISOString();
  const logEntry = { timestamp, level, message };
  appData.logs.push(logEntry);
  console.log(`[${timestamp}] ${level.toUpperCase()}: ${message}`);

  // Keep only last 1000 log entries
  if (appData.logs.length > 1000) {
    appData.logs.shift();
  }
}

function createResponse(data: any, status = 200) {
  return new Response(JSON.stringify(data), {
    status,
    headers: {
      "Content-Type": "application/json",
      "Access-Control-Allow-Origin": "*",
      "Access-Control-Allow-Methods": "GET, POST, PUT, DELETE, OPTIONS",
      "Access-Control-Allow-Headers": "Content-Type, Authorization",
    },
  });
}

function createErrorResponse(message: string, status = 400) {
  return createResponse({ error: message, success: false }, status);
}

// API Routes
const routes = {
  // Health check
  "GET /health": () => {
    return createResponse({
      status: "healthy",
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
      version: "1.0.0",
    });
  },

  // Better Auth routes - delegate to Better Auth handler
  "POST /api/auth/sign-up": async (request: Request) => {
    return auth.handler(request);
  },

  "POST /api/auth/sign-in": async (request: Request) => {
    return auth.handler(request);
  },

  "POST /api/auth/sign-out": async (request: Request) => {
    return auth.handler(request);
  },

  "GET /api/auth/session": async (request: Request) => {
    return auth.handler(request);
  },

  // Legacy API endpoints for backward compatibility
  "POST /api/auth/register": async (request: Request) => {
    try {
      const body = await request.json();
      const { username, email, password, confirmPassword } = body;

      // Validate input
      if (!username || !email || !password || !confirmPassword) {
        return createErrorResponse("All fields are required");
      }

      if (password !== confirmPassword) {
        return createErrorResponse("Passwords do not match");
      }

      // Use Better Auth's sign-up endpoint
      const signUpRequest = new Request(request.url.replace('/register', '/sign-up'), {
        method: 'POST',
        headers: request.headers,
        body: JSON.stringify({
          email,
          password,
          name: username,
        }),
      });

      const response = await auth.handler(signUpRequest);
      const data = await response.json();

      if (response.ok) {
        log("info", `New user registered: ${username} (${email})`);
        return createResponse({
          success: true,
          message: "User registered successfully",
          user: data.user,
        });
      } else {
        return createErrorResponse(data.error?.message || "Registration failed");
      }
    } catch (error) {
      log("error", `Registration error: ${error}`);
      return createErrorResponse("Registration failed", 500);
    }
  },

  "POST /api/auth/login": async (request: Request) => {
    try {
      const body = await request.json();
      const { emailOrUsername, password, rememberMe } = body;

      // Validate input
      if (!emailOrUsername || !password) {
        return createErrorResponse("Email/username and password are required");
      }

      // Use Better Auth's sign-in endpoint
      const signInRequest = new Request(request.url.replace('/login', '/sign-in'), {
        method: 'POST',
        headers: request.headers,
        body: JSON.stringify({
          email: emailOrUsername,
          password,
          rememberMe,
        }),
      });

      const response = await auth.handler(signInRequest);
      const data = await response.json();

      if (response.ok) {
        log("info", `User logged in: ${data.user?.email || emailOrUsername}`);

        // Extract session from response headers or cookies
        const sessionCookie = response.headers.get('set-cookie');
        let sessionId = null;
        if (sessionCookie) {
          const match = sessionCookie.match(/better-auth\.session_token=([^;]+)/);
          sessionId = match ? match[1] : null;
        }

        return createResponse({
          success: true,
          message: "Login successful",
          sessionId: sessionId,
          user: data.user,
        });
      } else {
        return createErrorResponse(data.error?.message || "Invalid credentials");
      }
    } catch (error) {
      log("error", `Login error: ${error}`);
      return createErrorResponse("Login failed", 500);
    }
  },

  "POST /api/auth/logout": async (request: Request) => {
    try {
      // Use Better Auth's sign-out endpoint
      const signOutRequest = new Request(request.url.replace('/logout', '/sign-out'), {
        method: 'POST',
        headers: request.headers,
        body: request.body,
      });

      const response = await auth.handler(signOutRequest);

      if (response.ok) {
        log("info", "User logged out");
        return createResponse({
          success: true,
          message: "Logout successful",
        });
      } else {
        return createErrorResponse("Logout failed", 500);
      }
    } catch (error) {
      log("error", `Logout error: ${error}`);
      return createErrorResponse("Logout failed", 500);
    }
  },

  "GET /api/auth/validate": async (request: Request) => {
    try {
      // Use Better Auth's session endpoint
      const sessionRequest = new Request(request.url.replace('/validate', '/session'), {
        method: 'GET',
        headers: request.headers,
      });

      const response = await auth.handler(sessionRequest);
      const data = await response.json();

      if (response.ok && data.session) {
        return createResponse({
          success: true,
          valid: true,
          user: data.user,
          session: data.session,
        });
      } else {
        return createErrorResponse("Invalid or expired session", 401);
      }
    } catch (error) {
      log("error", `Session validation error: ${error}`);
      return createErrorResponse("Validation failed", 500);
    }
  },

  // Get application settings
  "GET /api/settings": () => {
    log("info", "Settings requested");
    return createResponse({
      success: true,
      data: appData.settings,
    });
  },

  // Update application settings
  "POST /api/settings": async (request: Request) => {
    try {
      const body = await request.json();
      appData.settings = { ...appData.settings, ...body };
      log("info", `Settings updated: ${JSON.stringify(body)}`);
      
      return createResponse({
        success: true,
        message: "Settings updated successfully",
        data: appData.settings,
      });
    } catch (error) {
      log("error", `Failed to update settings: ${error}`);
      return createErrorResponse("Invalid JSON in request body");
    }
  },

  // Get user preferences
  "GET /api/preferences": () => {
    log("info", "User preferences requested");
    return createResponse({
      success: true,
      data: appData.userPreferences,
    });
  },

  // Update user preferences
  "POST /api/preferences": async (request: Request) => {
    try {
      const body = await request.json();
      appData.userPreferences = { ...appData.userPreferences, ...body };
      log("info", `User preferences updated: ${JSON.stringify(body)}`);
      
      return createResponse({
        success: true,
        message: "Preferences updated successfully",
        data: appData.userPreferences,
      });
    } catch (error) {
      log("error", `Failed to update preferences: ${error}`);
      return createErrorResponse("Invalid JSON in request body");
    }
  },

  // Get application logs
  "GET /api/logs": () => {
    const limit = 100; // Return last 100 logs
    const recentLogs = appData.logs.slice(-limit);
    
    return createResponse({
      success: true,
      data: recentLogs,
      total: appData.logs.length,
    });
  },

  // System information
  "GET /api/system": () => {
    return createResponse({
      success: true,
      data: {
        platform: process.platform,
        arch: process.arch,
        nodeVersion: process.version,
        bunVersion: Bun.version,
        memory: process.memoryUsage(),
        uptime: process.uptime(),
      },
    });
  },

  // Test endpoint for frontend
  "GET /api/test": () => {
    log("info", "Test endpoint called");
    return createResponse({
      success: true,
      message: "Backend is working correctly!",
      timestamp: new Date().toISOString(),
    });
  },

  // File operations (placeholder)
  "POST /api/files/save": async (request: Request) => {
    try {
      const body = await request.json();
      const { filename, content } = body;
      
      if (!filename || content === undefined) {
        return createErrorResponse("Filename and content are required");
      }
      
      // In a real app, you would save to filesystem
      log("info", `File save requested: ${filename}`);
      
      return createResponse({
        success: true,
        message: `File ${filename} saved successfully`,
        filename,
        size: content.length,
      });
    } catch (error) {
      log("error", `File save failed: ${error}`);
      return createErrorResponse("Failed to save file");
    }
  },

  "GET /api/files/list": () => {
    // In a real app, you would list actual files
    const mockFiles = [
      { name: "document1.txt", size: 1024, modified: new Date().toISOString() },
      { name: "document2.txt", size: 2048, modified: new Date().toISOString() },
    ];
    
    return createResponse({
      success: true,
      data: mockFiles,
    });
  },
};

// Request handler
async function handleRequest(request: Request): Promise<Response> {
  const url = new URL(request.url);
  const method = request.method;
  const path = url.pathname;
  const routeKey = `${method} ${path}`;

  log("info", `${method} ${path}`);

  // CORS headers
  const corsHeaders = {
    "Access-Control-Allow-Origin": "*",
    "Access-Control-Allow-Methods": "GET, POST, PUT, DELETE, OPTIONS",
    "Access-Control-Allow-Headers": "Content-Type, Authorization",
  };

  // Handle CORS preflight
  if (method === "OPTIONS") {
    return new Response(null, {
      status: 200,
      headers: corsHeaders,
    });
  }

  // Check if this is a Better Auth route (not handled by our custom routes)
  if (path.startsWith('/api/auth/') && !routes[routeKey as keyof typeof routes]) {
    try {
      const response = await auth.handler(request);
      // Add CORS headers to Better Auth responses
      Object.entries(corsHeaders).forEach(([key, value]) => {
        response.headers.set(key, value);
      });
      return response;
    } catch (error) {
      log("error", `Better Auth handler error: ${error}`);
      return createErrorResponse("Authentication error", 500);
    }
  }

  // Find matching route
  const handler = routes[routeKey as keyof typeof routes];

  if (handler) {
    try {
      const response = await handler(request);
      // Add CORS headers to custom route responses
      Object.entries(corsHeaders).forEach(([key, value]) => {
        response.headers.set(key, value);
      });
      return response;
    } catch (error) {
      log("error", `Route handler error: ${error}`);
      return createErrorResponse("Internal server error", 500);
    }
  }

  // 404 for unknown routes
  log("warn", `Route not found: ${routeKey}`);
  return createErrorResponse("Route not found", 404);
}

// Start the server
const server = serve({
  port: PORT,
  hostname: HOST,
  fetch: handleRequest,
});

log("info", `🚀 Bun server started on http://${HOST}:${PORT}`);
log("info", `📊 Health check available at http://${HOST}:${PORT}/health`);
log("info", `🔧 API endpoints available at http://${HOST}:${PORT}/api/*`);

// Graceful shutdown
process.on("SIGINT", () => {
  log("info", "Received SIGINT, shutting down gracefully");
  server.stop();
  process.exit(0);
});

process.on("SIGTERM", () => {
  log("info", "Received SIGTERM, shutting down gracefully");
  server.stop();
  process.exit(0);
});

export default server;
