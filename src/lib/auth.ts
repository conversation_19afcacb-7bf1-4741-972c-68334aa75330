import { betterAuth } from "better-auth";
import Database from "better-sqlite3";
import { join } from "path";

// Create database path - store in user data directory for Electron apps
const dbPath = join(process.cwd(), "tini-auth.db");

export const auth = betterAuth({
  database: new Database(dbPath),
  emailAndPassword: {
    enabled: true,
    autoSignIn: true, // Automatically sign in after registration
  },
  session: {
    expiresIn: 60 * 60 * 24 * 7, // 7 days
    updateAge: 60 * 60 * 24, // 1 day (session refresh)
    cookieCache: {
      enabled: true,
      maxAge: 5 * 60, // 5 minutes cache
    },
  },
  // Rate limiting configuration
  rateLimit: {
    window: 60, // 1 minute window
    max: 5, // 5 attempts per window
  },
  // Security configuration
  secret: process.env.BETTER_AUTH_SECRET || "your-secret-key-change-in-production",
  baseURL: process.env.BETTER_AUTH_URL || "http://localhost:3001",
  // Advanced configuration
  advanced: {
    generateId: () => {
      // Generate custom ID format similar to our current system
      return Math.random().toString(36).substring(2) + Date.now().toString(36);
    },
  },
});

// Export types for TypeScript
export type Session = typeof auth.$Infer.Session;
export type User = typeof auth.$Infer.User;
