// Main application JavaScript for the renderer process

// Global state
let currentTheme = 'light';
let currentSection = 'dashboard';

// Initialize the application
document.addEventListener('DOMContentLoaded', async () => {
    console.log('Tini Desktop App initialized');
    
    // Load app information
    await loadAppInfo();
    
    // Set up event listeners
    setupEventListeners();
    
    // Load saved theme
    loadSavedTheme();
    
    // Show initial section
    showSection('dashboard');
    
    // Test backend connection on startup
    testBackendConnection();
});

// Load application information
async function loadAppInfo() {
    try {
        if (window.electronAPI) {
            const version = await window.electronAPI.getAppVersion();
            const platform = await window.electronAPI.getPlatform();
            
            document.getElementById('app-version').textContent = version;
            document.getElementById('app-platform').textContent = platform;
            
            if (window.nodeAPI) {
                document.getElementById('electron-version').textContent = window.nodeAPI.versions.electron;
            }
        }
    } catch (error) {
        console.error('Failed to load app info:', error);
    }
}

// Set up event listeners
function setupEventListeners() {
    // Menu event listeners
    if (window.electronAPI) {
        window.electronAPI.onMenuNewFile(() => {
            console.log('New file requested');
            showNotification('New File', 'Creating new file...');
        });

        window.electronAPI.onMenuOpenFile(() => {
            console.log('Open file requested');
            showNotification('Open File', 'Opening file dialog...');
        });

        window.electronAPI.onMenuAbout(() => {
            document.getElementById('about-modal').showModal();
        });
    }

    // Keyboard shortcuts
    document.addEventListener('keydown', (event) => {
        if (event.ctrlKey || event.metaKey) {
            switch (event.key) {
                case '1':
                    event.preventDefault();
                    showSection('dashboard');
                    break;
                case '2':
                    event.preventDefault();
                    showSection('features');
                    break;
                case '3':
                    event.preventDefault();
                    showSection('settings');
                    break;
                case '4':
                    event.preventDefault();
                    showSection('about');
                    break;
            }
        }
    });
}

// Theme management
function setTheme(theme) {
    currentTheme = theme;
    document.documentElement.setAttribute('data-theme', theme);
    localStorage.setItem('theme', theme);
    
    // Update theme selector
    const selects = document.querySelectorAll('select');
    selects.forEach(select => {
        if (select.onchange && select.onchange.toString().includes('setTheme')) {
            select.value = theme;
        }
    });
    
    console.log(`Theme changed to: ${theme}`);
}

function loadSavedTheme() {
    const savedTheme = localStorage.getItem('theme') || 'light';
    setTheme(savedTheme);
}

// Section navigation
function showSection(sectionName) {
    // Hide all sections
    const sections = document.querySelectorAll('.content-section');
    sections.forEach(section => {
        section.classList.add('hidden');
    });
    
    // Show selected section
    const targetSection = document.getElementById(`${sectionName}-section`);
    if (targetSection) {
        targetSection.classList.remove('hidden');
        targetSection.classList.add('fade-in');
    }
    
    // Update navigation
    const menuItems = document.querySelectorAll('.menu-item');
    menuItems.forEach(item => {
        item.classList.remove('active');
    });
    
    const activeItem = document.querySelector(`[onclick="showSection('${sectionName}')"]`);
    if (activeItem) {
        activeItem.classList.add('active');
    }
    
    currentSection = sectionName;
    console.log(`Switched to section: ${sectionName}`);
}

// Backend communication
async function testBackendConnection() {
    const statusIndicator = document.getElementById('status-indicator');
    
    try {
        statusIndicator.textContent = 'Connecting...';
        statusIndicator.className = 'text-warning';
        
        // Simulate backend connection test
        // In a real app, this would communicate with the Bun backend
        await new Promise(resolve => setTimeout(resolve, 1000));
        
        statusIndicator.textContent = 'Connected';
        statusIndicator.className = 'text-success';
        
        showNotification('Backend Status', 'Successfully connected to backend server');
    } catch (error) {
        console.error('Backend connection failed:', error);
        statusIndicator.textContent = 'Disconnected';
        statusIndicator.className = 'text-error';
        
        showNotification('Backend Error', 'Failed to connect to backend server');
    }
}

// Notification system
function showNotification(title = 'Notification', message = 'This is a test notification') {
    // Create toast notification
    const toast = document.createElement('div');
    toast.className = 'toast toast-top toast-end z-50';
    toast.innerHTML = `
        <div class="alert alert-info">
            <div>
                <h3 class="font-bold">${title}</h3>
                <div class="text-xs">${message}</div>
            </div>
        </div>
    `;
    
    document.body.appendChild(toast);
    
    // Auto remove after 3 seconds
    setTimeout(() => {
        toast.remove();
    }, 3000);
    
    // Also use Electron notification if available
    if (window.electronAPI && window.electronAPI.showNotification) {
        window.electronAPI.showNotification(title, message);
    }
}

// Settings management
function openSettings() {
    showSection('settings');
}

// Development helpers
function openDevTools() {
    if (window.electronAPI && window.electronAPI.isDev) {
        // In development mode, dev tools can be opened via menu
        showNotification('Dev Tools', 'Use View > Toggle Developer Tools from the menu');
    } else {
        showNotification('Dev Tools', 'Developer tools are not available in production');
    }
}

// Loading state management
function showLoading() {
    document.getElementById('loading-overlay').classList.remove('hidden');
}

function hideLoading() {
    document.getElementById('loading-overlay').classList.add('hidden');
}

// Utility functions
function formatDate(date) {
    return new Intl.DateTimeFormat('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
    }).format(date);
}

function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// Export functions for global access
window.setTheme = setTheme;
window.showSection = showSection;
window.testBackendConnection = testBackendConnection;
window.showNotification = showNotification;
window.openSettings = openSettings;
window.openDevTools = openDevTools;

// Error handling
window.addEventListener('error', (event) => {
    console.error('Application error:', event.error);
    showNotification('Error', 'An unexpected error occurred');
});

window.addEventListener('unhandledrejection', (event) => {
    console.error('Unhandled promise rejection:', event.reason);
    showNotification('Error', 'An unexpected error occurred');
});

console.log('App.js loaded successfully');
