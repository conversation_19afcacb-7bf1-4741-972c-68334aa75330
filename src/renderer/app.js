// Main application JavaScript for the renderer process

// Global state
let currentTheme = 'light';
let currentSection = 'dashboard';
let currentUser = null;
let sessionId = null;

// Initialize the application
document.addEventListener('DOMContentLoaded', async () => {
    console.log('Tini Desktop App initialized');

    // Check authentication first
    const isAuthenticated = await checkAuthentication();
    if (!isAuthenticated) {
        // Redirect to auth page if not authenticated
        if (window.electronAPI) {
            window.electronAPI.navigateToAuth();
        } else {
            window.location.href = 'auth.html';
        }
        return;
    }

    // Load app information
    await loadAppInfo();

    // Set up event listeners
    setupEventListeners();

    // Load saved theme
    loadSavedTheme();

    // Update user interface with user info
    updateUserInterface();

    // Show initial section
    showSection('dashboard');

    // Test backend connection on startup
    testBackendConnection();
});

// Load application information
async function loadAppInfo() {
    try {
        if (window.electronAPI) {
            const version = await window.electronAPI.getAppVersion();
            const platform = await window.electronAPI.getPlatform();

            // Update version displays
            const versionElements = document.querySelectorAll('#app-version, .version-display');
            versionElements.forEach(el => el.textContent = version);

            // Update platform displays
            const platformElements = document.querySelectorAll('#app-platform, #platform-info');
            platformElements.forEach(el => el.textContent = platform);

            if (window.nodeAPI) {
                // Update system information
                const electronInfo = document.getElementById('electron-info');
                const nodeInfo = document.getElementById('node-info');
                const chromeInfo = document.getElementById('chrome-info');

                if (electronInfo) electronInfo.textContent = window.nodeAPI.versions.electron;
                if (nodeInfo) nodeInfo.textContent = window.nodeAPI.versions.node;
                if (chromeInfo) chromeInfo.textContent = window.nodeAPI.versions.chrome;

                // Update legacy elements
                const electronVersion = document.getElementById('electron-version');
                if (electronVersion) electronVersion.textContent = window.nodeAPI.versions.electron;
            }
        }
    } catch (error) {
        console.error('Failed to load app info:', error);
        // Set fallback values
        const fallbackElements = document.querySelectorAll('#platform-info, #electron-info, #node-info, #chrome-info');
        fallbackElements.forEach(el => {
            if (el) el.textContent = 'Unknown';
        });
    }
}

// Set up event listeners
function setupEventListeners() {
    // Menu event listeners
    if (window.electronAPI) {
        window.electronAPI.onMenuNewFile(() => {
            console.log('New file requested');
            showNotification('New File', 'Creating new file...');
        });

        window.electronAPI.onMenuOpenFile(() => {
            console.log('Open file requested');
            showNotification('Open File', 'Opening file dialog...');
        });

        window.electronAPI.onMenuAbout(() => {
            document.getElementById('about-modal').showModal();
        });
    }

    // Keyboard shortcuts
    document.addEventListener('keydown', (event) => {
        if (event.ctrlKey || event.metaKey) {
            switch (event.key) {
                case '1':
                    event.preventDefault();
                    showSection('dashboard');
                    break;
                case '2':
                    event.preventDefault();
                    showSection('features');
                    break;
                case '3':
                    event.preventDefault();
                    showSection('settings');
                    break;
                case '4':
                    event.preventDefault();
                    showSection('about');
                    break;
            }
        }
    });
}

// Theme management with enhanced visual feedback
function setTheme(theme) {
    currentTheme = theme;
    document.documentElement.setAttribute('data-theme', theme);
    localStorage.setItem('theme', theme);

    // Update theme selector
    const selects = document.querySelectorAll('select');
    selects.forEach(select => {
        if (select.onchange && select.onchange.toString().includes('setTheme')) {
            select.value = theme;
        }
    });

    // Update sidebar theme display
    const currentThemeDisplay = document.getElementById('current-theme');
    if (currentThemeDisplay) {
        currentThemeDisplay.textContent = theme;
    }

    // Add visual feedback for theme button
    const themeButton = document.querySelector('.dropdown [role="button"]');
    if (themeButton) {
        themeButton.classList.add('btn-success');
        setTimeout(() => {
            themeButton.classList.remove('btn-success');
        }, 300);
    }

    // Add visual feedback notification
    showNotification('Theme Changed', `Switched to ${theme} theme`);

    console.log(`Theme changed to: ${theme}`);
}

function loadSavedTheme() {
    const savedTheme = localStorage.getItem('theme') || 'light';
    setTheme(savedTheme);
}

// Section navigation
function showSection(sectionName) {
    // Hide all sections
    const sections = document.querySelectorAll('.content-section');
    sections.forEach(section => {
        section.classList.add('hidden');
    });
    
    // Show selected section
    const targetSection = document.getElementById(`${sectionName}-section`);
    if (targetSection) {
        targetSection.classList.remove('hidden');
        targetSection.classList.add('fade-in');
    }
    
    // Update navigation
    const menuItems = document.querySelectorAll('.menu-item');
    menuItems.forEach(item => {
        item.classList.remove('active');
    });
    
    const activeItem = document.querySelector(`[onclick="showSection('${sectionName}')"]`);
    if (activeItem) {
        activeItem.classList.add('active');
    }
    
    currentSection = sectionName;
    console.log(`Switched to section: ${sectionName}`);
}

// Backend communication
async function testBackendConnection() {
    const statusIndicator = document.getElementById('status-indicator');
    
    try {
        statusIndicator.textContent = 'Connecting...';
        statusIndicator.className = 'text-warning';
        
        // Simulate backend connection test
        // In a real app, this would communicate with the Bun backend
        await new Promise(resolve => setTimeout(resolve, 1000));
        
        statusIndicator.textContent = 'Connected';
        statusIndicator.className = 'text-success';
        
        showNotification('Backend Status', 'Successfully connected to backend server');
    } catch (error) {
        console.error('Backend connection failed:', error);
        statusIndicator.textContent = 'Disconnected';
        statusIndicator.className = 'text-error';
        
        showNotification('Backend Error', 'Failed to connect to backend server');
    }
}

// Notification system
function showNotification(title = 'Notification', message = 'This is a test notification') {
    // Create toast notification
    const toast = document.createElement('div');
    toast.className = 'toast toast-top toast-end z-50';
    toast.innerHTML = `
        <div class="alert alert-info">
            <div>
                <h3 class="font-bold">${title}</h3>
                <div class="text-xs">${message}</div>
            </div>
        </div>
    `;
    
    document.body.appendChild(toast);
    
    // Auto remove after 3 seconds
    setTimeout(() => {
        toast.remove();
    }, 3000);
    
    // Also use Electron notification if available
    if (window.electronAPI && window.electronAPI.showNotification) {
        window.electronAPI.showNotification(title, message);
    }
}

// Settings management
function openSettings() {
    showSection('settings');
}

// Development helpers
function openDevTools() {
    if (window.electronAPI && window.electronAPI.isDev) {
        // In development mode, dev tools can be opened via menu
        showNotification('Dev Tools', 'Use View > Toggle Developer Tools from the menu');
    } else {
        showNotification('Dev Tools', 'Developer tools are not available in production');
    }
}

// Loading state management
function showLoading() {
    document.getElementById('loading-overlay').classList.remove('hidden');
}

function hideLoading() {
    document.getElementById('loading-overlay').classList.add('hidden');
}

// Utility functions
function formatDate(date) {
    return new Intl.DateTimeFormat('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
    }).format(date);
}

function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// Authentication functions
async function checkAuthentication() {
    try {
        // Use Better Auth client to validate session
        const result = await window.betterAuthClient.validateSession();

        if (result.success && window.betterAuthClient.isAuthenticated()) {
            currentUser = window.betterAuthClient.getCurrentUser();
            return true;
        } else {
            // Session invalid
            sessionId = null;
            currentUser = null;
            return false;
        }
    } catch (error) {
        console.error('Authentication check failed:', error);
        // Clear invalid session data
        window.betterAuthClient.clearSession();
        sessionId = null;
        currentUser = null;
        return false;
    }
}

async function logout() {
    try {
        // Use Better Auth client to logout
        const result = await window.betterAuthClient.logout();

        if (result.success) {
            console.log('Logout successful');
        } else {
            console.error('Logout failed:', result.error);
        }
    } catch (error) {
        console.error('Logout request failed:', error);
    } finally {
        // Clear local state
        sessionId = null;
        currentUser = null;

        // Show notification
        showNotification('Logged Out', 'You have been successfully logged out');

        // Redirect to auth page
        setTimeout(() => {
            if (window.electronAPI) {
                window.electronAPI.navigateToAuth();
            } else {
                window.location.href = 'auth.html';
            }
        }, 1500);
    }
}

function updateUserInterface() {
    if (currentUser) {
        // Update user display in header or sidebar
        const userDisplays = document.querySelectorAll('.user-display');
        userDisplays.forEach(el => {
            el.textContent = currentUser.username;
        });

        // Update welcome message
        const welcomeMessage = document.querySelector('.welcome-message');
        if (welcomeMessage) {
            welcomeMessage.textContent = `Welcome back, ${currentUser.username}!`;
        }

        // Update user info in header
        updateUserInfo(currentUser);
    }
}

// Update user info display
function updateUserInfo(user) {
    if (user) {
        const userAvatar = document.getElementById('user-avatar');
        const userName = document.getElementById('user-name');
        const userEmail = document.getElementById('user-email');

        if (userAvatar && user.username) {
            userAvatar.textContent = user.username.charAt(0).toUpperCase();
        }
        if (userName && user.username) {
            userName.textContent = user.username;
        }
        if (userEmail && user.email) {
            userEmail.textContent = user.email;
        }
    }
}

function addLogoutButton() {
    const headerActions = document.querySelector('.app-header .flex.items-center.space-x-2');
    if (headerActions && !document.getElementById('logout-btn')) {
        const logoutButton = document.createElement('button');
        logoutButton.id = 'logout-btn';
        logoutButton.className = 'btn btn-ghost btn-sm';
        logoutButton.title = 'Logout';
        logoutButton.onclick = logout;
        logoutButton.innerHTML = `
            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1"></path>
            </svg>
        `;
        headerActions.appendChild(logoutButton);
    }
}

// Handle logout with confirmation
async function handleLogout() {
    try {
        // Show confirmation dialog
        const confirmed = confirm('Are you sure you want to logout?');
        if (!confirmed) return;

        // Call the existing logout function
        await logout();
    } catch (error) {
        console.error('Logout error:', error);
        // Force redirect on error
        if (window.electronAPI) {
            window.electronAPI.navigateToAuth();
        } else {
            window.location.href = 'auth.html';
        }
    }
}

// Export functions for global access
window.setTheme = setTheme;
window.showSection = showSection;
window.testBackendConnection = testBackendConnection;
window.showNotification = showNotification;
window.openSettings = openSettings;
window.openDevTools = openDevTools;
window.logout = logout;
window.handleLogout = handleLogout;
window.updateUserInfo = updateUserInfo;

// Error handling
window.addEventListener('error', (event) => {
    console.error('Application error:', event.error);
    showNotification('Error', 'An unexpected error occurred');
});

window.addEventListener('unhandledrejection', (event) => {
    console.error('Unhandled promise rejection:', event.reason);
    showNotification('Error', 'An unexpected error occurred');
});

console.log('App.js loaded successfully');
