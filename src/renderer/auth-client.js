// Better Auth Client for Frontend
// This provides a simplified interface to <PERSON> Auth for the Electron renderer

class BetterAuthClient {
  constructor(baseURL = 'http://localhost:3001') {
    this.baseURL = baseURL;
    this.sessionToken = null;
    this.user = null;
    
    // Load session from localStorage on initialization
    this.loadSession();
  }

  // Load session from localStorage
  loadSession() {
    try {
      const sessionData = localStorage.getItem('better-auth-session');
      if (sessionData) {
        const parsed = JSON.parse(sessionData);
        this.sessionToken = parsed.token;
        this.user = parsed.user;
      }
    } catch (error) {
      console.error('Failed to load session:', error);
      this.clearSession();
    }
  }

  // Save session to localStorage
  saveSession(token, user) {
    this.sessionToken = token;
    this.user = user;
    
    try {
      localStorage.setItem('better-auth-session', JSON.stringify({
        token,
        user,
        timestamp: Date.now()
      }));
    } catch (error) {
      console.error('Failed to save session:', error);
    }
  }

  // Clear session from localStorage
  clearSession() {
    this.sessionToken = null;
    this.user = null;
    localStorage.removeItem('better-auth-session');
    localStorage.removeItem('sessionId'); // Legacy cleanup
    localStorage.removeItem('user'); // Legacy cleanup
  }

  // Get headers for authenticated requests
  getHeaders() {
    const headers = {
      'Content-Type': 'application/json',
    };

    if (this.sessionToken) {
      headers['Authorization'] = `Bearer ${this.sessionToken}`;
      headers['Cookie'] = `better-auth.session_token=${this.sessionToken}`;
    }

    return headers;
  }

  // Make authenticated request
  async makeRequest(endpoint, options = {}) {
    const url = `${this.baseURL}${endpoint}`;
    
    const requestOptions = {
      ...options,
      headers: {
        ...this.getHeaders(),
        ...options.headers,
      },
    };

    try {
      const response = await fetch(url, requestOptions);
      
      // Check for session token in response
      const setCookie = response.headers.get('set-cookie');
      if (setCookie) {
        const tokenMatch = setCookie.match(/better-auth\.session_token=([^;]+)/);
        if (tokenMatch) {
          this.sessionToken = tokenMatch[1];
        }
      }

      return response;
    } catch (error) {
      console.error('Request failed:', error);
      throw error;
    }
  }

  // Sign up new user
  async signUp(email, password, name) {
    try {
      const response = await this.makeRequest('/api/auth/sign-up', {
        method: 'POST',
        body: JSON.stringify({
          email,
          password,
          name,
        }),
      });

      const data = await response.json();

      if (response.ok) {
        if (data.user && data.session) {
          this.saveSession(data.session.token, data.user);
        }
        return { success: true, user: data.user, session: data.session };
      } else {
        return { success: false, error: data.error?.message || 'Registration failed' };
      }
    } catch (error) {
      console.error('Sign up error:', error);
      return { success: false, error: 'Network error during registration' };
    }
  }

  // Sign in user
  async signIn(email, password, rememberMe = false) {
    try {
      const response = await this.makeRequest('/api/auth/sign-in', {
        method: 'POST',
        body: JSON.stringify({
          email,
          password,
          rememberMe,
        }),
      });

      const data = await response.json();

      if (response.ok) {
        if (data.user && data.session) {
          this.saveSession(data.session.token, data.user);
        }
        return { success: true, user: data.user, session: data.session };
      } else {
        return { success: false, error: data.error?.message || 'Login failed' };
      }
    } catch (error) {
      console.error('Sign in error:', error);
      return { success: false, error: 'Network error during login' };
    }
  }

  // Sign out user
  async signOut() {
    try {
      const response = await this.makeRequest('/api/auth/sign-out', {
        method: 'POST',
      });

      // Clear session regardless of response
      this.clearSession();

      if (response.ok) {
        return { success: true };
      } else {
        return { success: false, error: 'Logout failed on server' };
      }
    } catch (error) {
      console.error('Sign out error:', error);
      // Still clear local session even if server request fails
      this.clearSession();
      return { success: false, error: 'Network error during logout' };
    }
  }

  // Get current session
  async getSession() {
    try {
      const response = await this.makeRequest('/api/auth/session', {
        method: 'GET',
      });

      const data = await response.json();

      if (response.ok && data.session) {
        this.saveSession(data.session.token, data.user);
        return { success: true, user: data.user, session: data.session };
      } else {
        this.clearSession();
        return { success: false, error: 'No valid session' };
      }
    } catch (error) {
      console.error('Get session error:', error);
      this.clearSession();
      return { success: false, error: 'Failed to validate session' };
    }
  }

  // Check if user is authenticated
  isAuthenticated() {
    return !!(this.sessionToken && this.user);
  }

  // Get current user
  getCurrentUser() {
    return this.user;
  }

  // Legacy compatibility methods for existing frontend code
  async register(username, email, password, confirmPassword) {
    if (password !== confirmPassword) {
      return { success: false, error: 'Passwords do not match' };
    }
    
    return await this.signUp(email, password, username);
  }

  async login(emailOrUsername, password, rememberMe = false) {
    return await this.signIn(emailOrUsername, password, rememberMe);
  }

  async logout() {
    return await this.signOut();
  }

  async validateSession() {
    return await this.getSession();
  }
}

// Create global instance
window.betterAuthClient = new BetterAuthClient();

// Export for module usage
if (typeof module !== 'undefined' && module.exports) {
  module.exports = BetterAuthClient;
}
