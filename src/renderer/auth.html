<!DOCTYPE html>
<html lang="en" data-theme="light">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="Content-Security-Policy" content="default-src 'self'; connect-src 'self' http://localhost:3001 ws://localhost:*; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline'; img-src 'self' data: blob:;">
    <title>Tini Desktop App - Authentication</title>
    <link rel="stylesheet" href="style.css">
</head>
<body class="bg-base-200">
    <div class="min-h-screen flex items-center justify-center p-4">
        <!-- Authentication Container -->
        <div class="card w-full max-w-md bg-base-100 shadow-2xl">
            <div class="card-body">
                <!-- Header -->
                <div class="text-center mb-6">
                    <div class="w-16 h-16 bg-gradient-to-br from-primary to-secondary rounded-full flex items-center justify-center mx-auto mb-4">
                        <svg class="w-8 h-8 text-primary-content" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                    </div>
                    <h1 class="text-2xl font-bold text-base-content">Tini Desktop App</h1>
                    <p class="text-base-content/70 text-sm mt-1">Sign in to continue</p>
                </div>

                <!-- Login Form -->
                <form id="login-form" class="space-y-4">
                    <div class="form-control">
                        <label class="label">
                            <span class="label-text">Email or Username</span>
                        </label>
                        <input 
                            type="text" 
                            id="emailOrUsername"
                            placeholder="Enter your email or username" 
                            class="input input-bordered w-full" 
                            required
                        />
                        <label class="label">
                            <span class="label-text-alt text-error" id="emailOrUsername-error"></span>
                        </label>
                    </div>

                    <div class="form-control">
                        <label class="label">
                            <span class="label-text">Password</span>
                        </label>
                        <div class="relative">
                            <input 
                                type="password" 
                                id="password"
                                placeholder="Enter your password" 
                                class="input input-bordered w-full pr-10" 
                                required
                            />
                            <button 
                                type="button" 
                                class="absolute inset-y-0 right-0 pr-3 flex items-center"
                                onclick="togglePasswordVisibility('password')"
                            >
                                <svg class="w-5 h-5 text-base-content/50" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                                </svg>
                            </button>
                        </div>
                        <label class="label">
                            <span class="label-text-alt text-error" id="password-error"></span>
                        </label>
                    </div>

                    <div class="form-control">
                        <label class="cursor-pointer label justify-start gap-3">
                            <input type="checkbox" id="rememberMe" class="checkbox checkbox-primary" />
                            <span class="label-text">Remember me for 30 days</span>
                        </label>
                    </div>

                    <div class="form-control mt-6">
                        <button type="submit" class="btn btn-primary w-full" id="login-btn">
                            <span class="loading loading-spinner loading-sm hidden" id="login-loading"></span>
                            <span id="login-text">Sign In</span>
                        </button>
                    </div>

                    <!-- Error Message -->
                    <div class="alert alert-error hidden" id="login-error">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                        <span id="login-error-text"></span>
                    </div>

                    <!-- Success Message -->
                    <div class="alert alert-success hidden" id="login-success">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                        <span id="login-success-text"></span>
                    </div>
                </form>

                <!-- Register Form (Hidden by default) -->
                <form id="register-form" class="space-y-4 hidden">
                    <div class="form-control">
                        <label class="label">
                            <span class="label-text">Username</span>
                        </label>
                        <input 
                            type="text" 
                            id="reg-username"
                            placeholder="Choose a username" 
                            class="input input-bordered w-full" 
                            required
                        />
                        <label class="label">
                            <span class="label-text-alt text-error" id="reg-username-error"></span>
                        </label>
                    </div>

                    <div class="form-control">
                        <label class="label">
                            <span class="label-text">Email</span>
                        </label>
                        <input 
                            type="email" 
                            id="reg-email"
                            placeholder="Enter your email" 
                            class="input input-bordered w-full" 
                            required
                        />
                        <label class="label">
                            <span class="label-text-alt text-error" id="reg-email-error"></span>
                        </label>
                    </div>

                    <div class="form-control">
                        <label class="label">
                            <span class="label-text">Password</span>
                        </label>
                        <div class="relative">
                            <input 
                                type="password" 
                                id="reg-password"
                                placeholder="Create a password" 
                                class="input input-bordered w-full pr-10" 
                                required
                            />
                            <button 
                                type="button" 
                                class="absolute inset-y-0 right-0 pr-3 flex items-center"
                                onclick="togglePasswordVisibility('reg-password')"
                            >
                                <svg class="w-5 h-5 text-base-content/50" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                                </svg>
                            </button>
                        </div>
                        <label class="label">
                            <span class="label-text-alt text-info">At least 6 characters with letters and numbers</span>
                            <span class="label-text-alt text-error" id="reg-password-error"></span>
                        </label>
                    </div>

                    <div class="form-control">
                        <label class="label">
                            <span class="label-text">Confirm Password</span>
                        </label>
                        <div class="relative">
                            <input 
                                type="password" 
                                id="reg-confirm-password"
                                placeholder="Confirm your password" 
                                class="input input-bordered w-full pr-10" 
                                required
                            />
                            <button 
                                type="button" 
                                class="absolute inset-y-0 right-0 pr-3 flex items-center"
                                onclick="togglePasswordVisibility('reg-confirm-password')"
                            >
                                <svg class="w-5 h-5 text-base-content/50" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                                </svg>
                            </button>
                        </div>
                        <label class="label">
                            <span class="label-text-alt text-error" id="reg-confirm-password-error"></span>
                        </label>
                    </div>

                    <div class="form-control mt-6">
                        <button type="submit" class="btn btn-primary w-full" id="register-btn">
                            <span class="loading loading-spinner loading-sm hidden" id="register-loading"></span>
                            <span id="register-text">Create Account</span>
                        </button>
                    </div>

                    <!-- Error Message -->
                    <div class="alert alert-error hidden" id="register-error">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                        <span id="register-error-text"></span>
                    </div>

                    <!-- Success Message -->
                    <div class="alert alert-success hidden" id="register-success">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                        <span id="register-success-text"></span>
                    </div>
                </form>

                <!-- Toggle between Login and Register -->
                <div class="divider">OR</div>
                <div class="text-center">
                    <button class="btn btn-ghost btn-sm" id="toggle-form">
                        <span id="toggle-text">Don't have an account? Sign up</span>
                    </button>
                </div>

                <!-- Theme Selector -->
                <div class="text-center mt-4">
                    <div class="dropdown dropdown-top">
                        <div tabindex="0" role="button" class="btn btn-ghost btn-xs gap-2">
                            <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M10 2a1 1 0 011 1v1a1 1 0 11-2 0V3a1 1 0 011-1zm4 8a4 4 0 11-8 0 4 4 0 018 0zm-.464 4.95l.707.707a1 1 0 001.414-1.414l-.707-.707a1 1 0 00-1.414 1.414zm2.12-10.607a1 1 0 010 1.414l-.706.707a1 1 0 11-1.414-1.414l.707-.707a1 1 0 011.414 0zM17 11a1 1 0 100-2h-1a1 1 0 100 2h1zm-7 4a1 1 0 011 1v1a1 1 0 11-2 0v-1a1 1 0 011-1zM5.05 6.464A1 1 0 106.465 5.05l-.708-.707a1 1 0 00-1.414 1.414l.707.707zm1.414 8.486l-.707.707a1 1 0 01-1.414-1.414l.707-.707a1 1 0 011.414 1.414zM4 11a1 1 0 100-2H3a1 1 0 000 2h1z" clip-rule="evenodd"></path>
                            </svg>
                            Theme
                        </div>
                        <ul tabindex="0" class="dropdown-content menu bg-base-100 rounded-box z-[1] w-40 p-2 shadow-lg border border-base-300">
                            <li><a onclick="setAuthTheme('light')">Light</a></li>
                            <li><a onclick="setAuthTheme('dark')">Dark</a></li>
                            <li><a onclick="setAuthTheme('cupcake')">Cupcake</a></li>
                            <li><a onclick="setAuthTheme('cyberpunk')">Cyberpunk</a></li>
                            <li><a onclick="setAuthTheme('dracula')">Dracula</a></li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="auth-client.js"></script>
    <script src="auth.js"></script>
</body>
</html>
