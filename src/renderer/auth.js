// Authentication JavaScript for Tini Desktop App

// Global state
let isLoginMode = true;
let currentTheme = 'light';

// DOM elements
const loginForm = document.getElementById('login-form');
const registerForm = document.getElementById('register-form');
const toggleButton = document.getElementById('toggle-form');
const toggleText = document.getElementById('toggle-text');

// Initialize authentication page
document.addEventListener('DOMContentLoaded', () => {
    loadSavedTheme();
    setupEventListeners();
    checkExistingSession();
});

// Load saved theme
function loadSavedTheme() {
    const savedTheme = localStorage.getItem('theme') || 'light';
    setTheme(savedTheme);
}

// Set theme for auth page (unified with main app)
function setTheme(theme) {
    currentTheme = theme;
    document.documentElement.setAttribute('data-theme', theme);
    localStorage.setItem('theme', theme);

    // Add visual feedback for theme change
    const themeButton = document.querySelector('.dropdown [role="button"]');
    if (themeButton) {
        themeButton.classList.add('btn-success');
        setTimeout(() => {
            themeButton.classList.remove('btn-success');
        }, 300);
    }

    console.log(`Theme changed to: ${theme}`);
}

// Export for global access
window.setTheme = setTheme;

// Setup event listeners
function setupEventListeners() {
    // Login form submission
    loginForm.addEventListener('submit', handleLogin);
    
    // Register form submission
    registerForm.addEventListener('submit', handleRegister);
    
    // Toggle between login and register
    toggleButton.addEventListener('click', toggleAuthMode);
    
    // Real-time validation
    setupRealTimeValidation();
}

// Toggle between login and register modes
function toggleAuthMode() {
    isLoginMode = !isLoginMode;
    
    if (isLoginMode) {
        loginForm.classList.remove('hidden');
        registerForm.classList.add('hidden');
        toggleText.textContent = "Don't have an account? Sign up";
        document.querySelector('h1 + p').textContent = 'Sign in to continue';
    } else {
        loginForm.classList.add('hidden');
        registerForm.classList.remove('hidden');
        toggleText.textContent = 'Already have an account? Sign in';
        document.querySelector('h1 + p').textContent = 'Create your account';
    }
    
    // Clear any existing errors
    clearErrors();
}

// Handle login form submission
async function handleLogin(event) {
    event.preventDefault();

    const emailOrUsername = document.getElementById('emailOrUsername').value.trim();
    const password = document.getElementById('password').value;
    const rememberMe = document.getElementById('rememberMe').checked;

    // Clear previous errors
    clearErrors();

    // Validate inputs
    if (!validateLoginInputs(emailOrUsername, password)) {
        return;
    }

    // Show loading state
    setLoadingState('login', true);

    try {
        // Use Better Auth client
        const result = await window.betterAuthClient.login(emailOrUsername, password, rememberMe);

        if (result.success) {
            // Show success message
            showMessage('login', 'success', 'Login successful! Redirecting...');

            // Redirect to main app after short delay
            setTimeout(() => {
                if (window.electronAPI) {
                    window.electronAPI.navigateToMain();
                } else {
                    window.location.href = 'index.html';
                }
            }, 1500);
        } else {
            showMessage('login', 'error', result.error || 'Login failed');
        }
    } catch (error) {
        console.error('Login error:', error);
        showMessage('login', 'error', 'Network error. Please check your connection.');
    } finally {
        setLoadingState('login', false);
    }
}

// Handle register form submission
async function handleRegister(event) {
    event.preventDefault();

    const username = document.getElementById('reg-username').value.trim();
    const email = document.getElementById('reg-email').value.trim();
    const password = document.getElementById('reg-password').value;
    const confirmPassword = document.getElementById('reg-confirm-password').value;

    // Clear previous errors
    clearErrors();

    // Validate inputs
    if (!validateRegisterInputs(username, email, password, confirmPassword)) {
        return;
    }

    // Show loading state
    setLoadingState('register', true);

    try {
        // Use Better Auth client
        const result = await window.betterAuthClient.register(username, email, password, confirmPassword);

        if (result.success) {
            showMessage('register', 'success', 'Account created successfully! Please sign in.');

            // Switch to login mode after successful registration
            setTimeout(() => {
                toggleAuthMode();
                // Pre-fill the email/username field
                document.getElementById('emailOrUsername').value = email;
            }, 2000);
        } else {
            showMessage('register', 'error', result.error || 'Registration failed');
        }
    } catch (error) {
        console.error('Registration error:', error);
        showMessage('register', 'error', 'Network error. Please check your connection.');
    } finally {
        setLoadingState('register', false);
    }
}

// Validate login inputs
function validateLoginInputs(emailOrUsername, password) {
    let isValid = true;
    
    if (!emailOrUsername) {
        showFieldError('emailOrUsername', 'Email or username is required');
        isValid = false;
    }
    
    if (!password) {
        showFieldError('password', 'Password is required');
        isValid = false;
    }
    
    return isValid;
}

// Validate register inputs
function validateRegisterInputs(username, email, password, confirmPassword) {
    let isValid = true;
    
    if (!username || username.length < 3) {
        showFieldError('reg-username', 'Username must be at least 3 characters');
        isValid = false;
    }
    
    if (!email || !isValidEmail(email)) {
        showFieldError('reg-email', 'Please enter a valid email address');
        isValid = false;
    }
    
    if (!password || !isValidPassword(password)) {
        showFieldError('reg-password', 'Password must be at least 6 characters with letters and numbers');
        isValid = false;
    }
    
    if (password !== confirmPassword) {
        showFieldError('reg-confirm-password', 'Passwords do not match');
        isValid = false;
    }
    
    return isValid;
}

// Email validation
function isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
}

// Password validation
function isValidPassword(password) {
    return password.length >= 6 && /[a-zA-Z]/.test(password) && /[0-9]/.test(password);
}

// Show field-specific error
function showFieldError(fieldId, message) {
    const errorElement = document.getElementById(`${fieldId}-error`);
    if (errorElement) {
        errorElement.textContent = message;
    }
    
    const inputElement = document.getElementById(fieldId);
    if (inputElement) {
        inputElement.classList.add('input-error');
    }
}

// Show success or error message
function showMessage(formType, type, message) {
    const messageElement = document.getElementById(`${formType}-${type}`);
    const textElement = document.getElementById(`${formType}-${type}-text`);
    
    if (messageElement && textElement) {
        textElement.textContent = message;
        messageElement.classList.remove('hidden');
        
        // Hide after 5 seconds for error messages
        if (type === 'error') {
            setTimeout(() => {
                messageElement.classList.add('hidden');
            }, 5000);
        }
    }
}

// Clear all errors
function clearErrors() {
    // Clear field errors
    const errorElements = document.querySelectorAll('[id$="-error"]');
    errorElements.forEach(el => el.textContent = '');
    
    // Remove error styling from inputs
    const inputs = document.querySelectorAll('.input-error');
    inputs.forEach(input => input.classList.remove('input-error'));
    
    // Hide alert messages
    const alerts = document.querySelectorAll('.alert');
    alerts.forEach(alert => alert.classList.add('hidden'));
}

// Set loading state
function setLoadingState(formType, isLoading) {
    const button = document.getElementById(`${formType}-btn`);
    const loading = document.getElementById(`${formType}-loading`);
    const text = document.getElementById(`${formType}-text`);
    
    if (isLoading) {
        button.disabled = true;
        loading.classList.remove('hidden');
        text.textContent = formType === 'login' ? 'Signing In...' : 'Creating Account...';
    } else {
        button.disabled = false;
        loading.classList.add('hidden');
        text.textContent = formType === 'login' ? 'Sign In' : 'Create Account';
    }
}

// Toggle password visibility
function togglePasswordVisibility(fieldId) {
    const input = document.getElementById(fieldId);
    const type = input.getAttribute('type') === 'password' ? 'text' : 'password';
    input.setAttribute('type', type);
}

// Setup real-time validation
function setupRealTimeValidation() {
    // Email validation on blur
    document.getElementById('reg-email')?.addEventListener('blur', function() {
        if (this.value && !isValidEmail(this.value)) {
            showFieldError('reg-email', 'Please enter a valid email address');
        }
    });
    
    // Password strength validation
    document.getElementById('reg-password')?.addEventListener('input', function() {
        if (this.value && !isValidPassword(this.value)) {
            showFieldError('reg-password', 'Password must be at least 6 characters with letters and numbers');
        } else if (this.value) {
            document.getElementById('reg-password-error').textContent = '';
            this.classList.remove('input-error');
        }
    });
    
    // Confirm password validation
    document.getElementById('reg-confirm-password')?.addEventListener('input', function() {
        const password = document.getElementById('reg-password').value;
        if (this.value && this.value !== password) {
            showFieldError('reg-confirm-password', 'Passwords do not match');
        } else if (this.value) {
            document.getElementById('reg-confirm-password-error').textContent = '';
            this.classList.remove('input-error');
        }
    });
}

// Check for existing session
async function checkExistingSession() {
    try {
        // Use Better Auth client to validate session
        const result = await window.betterAuthClient.validateSession();

        if (result.success && window.betterAuthClient.isAuthenticated()) {
            // Session is valid, redirect to main app
            if (window.electronAPI) {
                window.electronAPI.navigateToMain();
            } else {
                window.location.href = 'index.html';
            }
            return;
        }
    } catch (error) {
        console.error('Session validation error:', error);
        // Clear any invalid session data
        window.betterAuthClient.clearSession();
    }
}
