<!DOCTYPE html>
<html lang="en" data-theme="light">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="Content-Security-Policy" content="default-src 'self'; connect-src 'self' http://localhost:3001 ws://localhost:*; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline'; img-src 'self' data: blob:;">
    <title>Tini Desktop App</title>
    <link rel="stylesheet" href="style.css">
</head>
<body>
    <div class="app-container">
        <!-- Header -->
        <header class="app-header">
            <div class="flex items-center space-x-4">
                <div class="flex items-center space-x-3">
                    <div class="w-8 h-8 bg-gradient-to-br from-primary to-secondary rounded-lg flex items-center justify-center">
                        <svg class="w-5 h-5 text-primary-content" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                    </div>
                    <div>
                        <h1 class="text-xl font-bold text-primary">Tini Desktop App</h1>
                        <div class="text-xs text-base-content/60">
                            <span class="welcome-message">Built with Bun + Electron + DaisyUI</span>
                        </div>
                    </div>
                </div>
                <div class="badge badge-secondary badge-sm">v1.0.0</div>
            </div>

            <div class="flex items-center space-x-2">
                <!-- User Info -->
                <div class="hidden sm:flex items-center space-x-3 mr-4">
                    <div class="avatar placeholder">
                        <div class="bg-primary text-primary-content rounded-full w-10 h-10 flex items-center justify-center">
                            <span class="text-sm font-medium" id="user-avatar">U</span>
                        </div>
                    </div>
                    <div class="text-sm">
                        <div class="font-medium text-base-content" id="user-name">User</div>
                        <div class="text-xs text-base-content/60" id="user-email"><EMAIL></div>
                    </div>
                </div>

                <!-- Theme Selector -->
                <div class="dropdown dropdown-end">
                    <div tabindex="0" role="button" class="btn btn-ghost btn-sm gap-2 transition-all duration-200 hover:bg-base-300">
                        <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M10 2a1 1 0 011 1v1a1 1 0 11-2 0V3a1 1 0 011-1zm4 8a4 4 0 11-8 0 4 4 0 018 0zm-.464 4.95l.707.707a1 1 0 001.414-1.414l-.707-.707a1 1 0 00-1.414 1.414zm2.12-10.607a1 1 0 010 1.414l-.706.707a1 1 0 11-1.414-1.414l.707-.707a1 1 0 011.414 0zM17 11a1 1 0 100-2h-1a1 1 0 100 2h1zm-7 4a1 1 0 011 1v1a1 1 0 11-2 0v-1a1 1 0 011-1zM5.05 6.464A1 1 0 106.465 5.05l-.708-.707a1 1 0 00-1.414 1.414l.707.707zm1.414 8.486l-.707.707a1 1 0 01-1.414-1.414l.707-.707a1 1 0 011.414 1.414zM4 11a1 1 0 100-2H3a1 1 0 000 2h1z" clip-rule="evenodd"></path>
                        </svg>
                        <span class="hidden sm:inline">Theme</span>
                    </div>
                    <ul tabindex="0" class="dropdown-content menu bg-base-100 rounded-box z-[1] w-52 p-2 shadow-lg border border-base-300">
                        <li><a onclick="setTheme('light')" class="gap-3 transition-colors duration-200">
                            <div class="w-3 h-3 rounded-full bg-base-100 border-2 border-base-300"></div>
                            Light
                        </a></li>
                        <li><a onclick="setTheme('dark')" class="gap-3 transition-colors duration-200">
                            <div class="w-3 h-3 rounded-full bg-base-900"></div>
                            Dark
                        </a></li>
                        <li><a onclick="setTheme('cupcake')" class="gap-3 transition-colors duration-200">
                            <div class="w-3 h-3 rounded-full bg-pink-300"></div>
                            Cupcake
                        </a></li>
                        <li><a onclick="setTheme('cyberpunk')" class="gap-3 transition-colors duration-200">
                            <div class="w-3 h-3 rounded-full bg-yellow-400"></div>
                            Cyberpunk
                        </a></li>
                        <li><a onclick="setTheme('dracula')" class="gap-3 transition-colors duration-200">
                            <div class="w-3 h-3 rounded-full bg-purple-500"></div>
                            Dracula
                        </a></li>
                    </ul>
                </div>

                <!-- Settings Button -->
                <button class="btn btn-ghost btn-sm transition-all duration-200 hover:bg-base-300" onclick="openSettings()" title="Settings">
                    <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M11.49 3.17c-.38-1.56-2.6-1.56-2.98 0a1.532 1.532 0 01-2.286.948c-1.372-.836-2.942.734-2.106 2.106.54.886.061 2.042-.947 2.287-1.561.379-1.561 2.6 0 2.978a1.532 1.532 0 01.947 2.287c-.836 1.372.734 2.942 2.106 2.106a1.532 1.532 0 012.287.947c.379 1.561 2.6 1.561 2.978 0a1.533 1.533 0 012.287-.947c1.372.836 2.942-.734 2.106-2.106a1.533 1.533 0 01.947-2.287c1.561-.379 1.561-2.6 0-2.978a1.532 1.532 0 01-.947-2.287c.836-1.372-.734-2.942-2.106-2.106a1.532 1.532 0 01-2.287-.947zM10 13a3 3 0 100-6 3 3 0 000 6z" clip-rule="evenodd"></path>
                    </svg>
                </button>

                <!-- Logout Button -->
                <button class="btn btn-ghost btn-sm text-error transition-all duration-200 hover:bg-error hover:text-error-content" onclick="handleLogout()" title="Logout">
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1"></path>
                    </svg>
                </button>
            </div>
        </header>

        <!-- Main Content Area -->
        <main class="app-main">
            <!-- Sidebar -->
            <aside class="app-sidebar">
                <div class="mb-6">
                    <h2 class="text-sm font-semibold text-base-content/70 uppercase tracking-wider mb-3">Navigation</h2>
                    <nav class="space-y-1">
                        <a href="#" onclick="showSection('dashboard')" class="menu-item active">
                            <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z"></path>
                            </svg>
                            <span>Dashboard</span>
                            <kbd class="kbd kbd-xs ml-auto">⌘1</kbd>
                        </a>
                        <a href="#" onclick="showSection('features')" class="menu-item">
                            <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M3 3a1 1 0 000 2v8a2 2 0 002 2h2.586l-1.293 1.293a1 1 0 101.414 1.414L10 15.414l2.293 2.293a1 1 0 001.414-1.414L12.414 15H15a2 2 0 002-2V5a1 1 0 100-2H3zm11.707 4.707a1 1 0 00-1.414-1.414L10 9.586 8.707 8.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                            </svg>
                            <span>Features</span>
                            <kbd class="kbd kbd-xs ml-auto">⌘2</kbd>
                        </a>
                        <a href="#" onclick="showSection('settings')" class="menu-item">
                            <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M11.49 3.17c-.38-1.56-2.6-1.56-2.98 0a1.532 1.532 0 01-2.286.948c-1.372-.836-2.942.734-2.106 2.106.54.886.061 2.042-.947 2.287-1.561.379-1.561 2.6 0 2.978a1.532 1.532 0 01.947 2.287c-.836 1.372.734 2.942 2.106 2.106a1.532 1.532 0 012.287.947c.379 1.561 2.6 1.561 2.978 0a1.533 1.533 0 012.287-.947c1.372.836 2.942-.734 2.106-2.106a1.533 1.533 0 01.947-2.287c1.561-.379 1.561-2.6 0-2.978a1.532 1.532 0 01-.947-2.287c.836-1.372-.734-2.942-2.106-2.106a1.532 1.532 0 01-2.287-.947zM10 13a3 3 0 100-6 3 3 0 000 6z" clip-rule="evenodd"></path>
                            </svg>
                            <span>Settings</span>
                            <kbd class="kbd kbd-xs ml-auto">⌘3</kbd>
                        </a>
                        <a href="#" onclick="showSection('about')" class="menu-item">
                            <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"></path>
                            </svg>
                            <span>About</span>
                            <kbd class="kbd kbd-xs ml-auto">⌘4</kbd>
                        </a>
                    </nav>
                </div>

                <!-- Quick Stats -->
                <div class="mt-8">
                    <h3 class="text-sm font-semibold text-base-content/70 uppercase tracking-wider mb-3">Status</h3>
                    <div class="space-y-3">
                        <div class="flex items-center justify-between text-sm">
                            <span class="text-base-content/70">Backend</span>
                            <div class="flex items-center gap-2">
                                <div class="status status-success status-xs"></div>
                                <span class="text-success text-xs">Online</span>
                            </div>
                        </div>
                        <div class="flex items-center justify-between text-sm">
                            <span class="text-base-content/70">Theme</span>
                            <span class="text-xs capitalize" id="current-theme">light</span>
                        </div>
                        <div class="flex items-center justify-between text-sm">
                            <span class="text-base-content/70">Version</span>
                            <span class="text-xs">1.0.0</span>
                        </div>
                    </div>
                </div>
            </aside>

            <!-- Content Area -->
            <div class="app-content">
                <!-- Dashboard Section -->
                <section id="dashboard-section" class="content-section">
                    <!-- Welcome Header -->
                    <div class="mb-8">
                        <div class="flex items-center gap-4 mb-4">
                            <div class="avatar placeholder">
                                <div class="bg-gradient-to-br from-primary to-secondary text-primary-content rounded-full w-16 h-16 flex items-center justify-center">
                                    <span class="text-2xl font-bold">T</span>
                                </div>
                            </div>
                            <div>
                                <h2 class="text-3xl font-bold text-base-content">Welcome to Tini Desktop App</h2>
                                <p class="text-base-content/70 text-lg">Built with modern web technologies for desktop</p>
                            </div>
                        </div>

                        <!-- Technology Badges -->
                        <div class="flex flex-wrap gap-2">
                            <div class="badge badge-primary gap-2">
                                <svg class="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
                                    <path d="M13 6a3 3 0 11-6 0 3 3 0 016 0zM18 8a2 2 0 11-4 0 2 2 0 014 0zM14 15a4 4 0 00-8 0v3h8v-3z"></path>
                                </svg>
                                Bun Runtime
                            </div>
                            <div class="badge badge-secondary gap-2">
                                <svg class="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
                                    <path d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z"></path>
                                </svg>
                                Electron
                            </div>
                            <div class="badge badge-accent gap-2">
                                <svg class="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M3 3a1 1 0 000 2v8a2 2 0 002 2h2.586l-1.293 1.293a1 1 0 101.414 1.414L10 15.414l2.293 2.293a1 1 0 001.414-1.414L12.414 15H15a2 2 0 002-2V5a1 1 0 100-2H3zm11.707 4.707a1 1 0 00-1.414-1.414L10 9.586 8.707 8.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                                </svg>
                                DaisyUI
                            </div>
                            <div class="badge badge-info gap-2">
                                <svg class="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M12.316 3.051a1 1 0 01.633 1.265l-4 12a1 1 0 11-1.898-.632l4-12a1 1 0 011.265-.633zM5.707 6.293a1 1 0 010 1.414L3.414 10l2.293 2.293a1 1 0 11-1.414 1.414l-3-3a1 1 0 010-1.414l3-3a1 1 0 011.414 0zm8.586 0a1 1 0 011.414 0l3 3a1 1 0 010 1.414l-3 3a1 1 0 11-1.414-1.414L16.586 10l-2.293-2.293a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                                </svg>
                                TypeScript
                            </div>
                        </div>
                    </div>

                    <!-- Technology Stats -->
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
                        <div class="stat bg-gradient-to-br from-primary to-primary/80 text-primary-content rounded-xl shadow-lg">
                            <div class="stat-figure">
                                <svg class="w-8 h-8 opacity-80" fill="currentColor" viewBox="0 0 20 20">
                                    <path d="M13 6a3 3 0 11-6 0 3 3 0 016 0zM18 8a2 2 0 11-4 0 2 2 0 014 0zM14 15a4 4 0 00-8 0v3h8v-3z"></path>
                                </svg>
                            </div>
                            <div class="stat-title text-primary-content/70">Runtime</div>
                            <div class="stat-value text-2xl">Bun</div>
                            <div class="stat-desc text-primary-content/70">4x faster than Node.js</div>
                        </div>

                        <div class="stat bg-gradient-to-br from-secondary to-secondary/80 text-secondary-content rounded-xl shadow-lg">
                            <div class="stat-figure">
                                <svg class="w-8 h-8 opacity-80" fill="currentColor" viewBox="0 0 20 20">
                                    <path d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z"></path>
                                </svg>
                            </div>
                            <div class="stat-title text-secondary-content/70">Framework</div>
                            <div class="stat-value text-2xl">Electron</div>
                            <div class="stat-desc text-secondary-content/70">Cross-platform desktop</div>
                        </div>

                        <div class="stat bg-gradient-to-br from-accent to-accent/80 text-accent-content rounded-xl shadow-lg">
                            <div class="stat-figure">
                                <svg class="w-8 h-8 opacity-80" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M3 3a1 1 0 000 2v8a2 2 0 002 2h2.586l-1.293 1.293a1 1 0 101.414 1.414L10 15.414l2.293 2.293a1 1 0 001.414-1.414L12.414 15H15a2 2 0 002-2V5a1 1 0 100-2H3zm11.707 4.707a1 1 0 00-1.414-1.414L10 9.586 8.707 8.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                                </svg>
                            </div>
                            <div class="stat-title text-accent-content/70">UI Library</div>
                            <div class="stat-value text-2xl">DaisyUI</div>
                            <div class="stat-desc text-accent-content/70">Beautiful components</div>
                        </div>
                    </div>

                    <!-- Quick Actions -->
                    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                        <div class="card bg-base-200 shadow-lg">
                            <div class="card-body">
                                <h3 class="card-title flex items-center gap-2">
                                    <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M11.3 1.046A1 1 0 0112 2v5h4a1 1 0 01.82 1.573l-7 10A1 1 0 018 18v-5H4a1 1 0 01-.82-1.573l7-10a1 1 0 011.12-.38z" clip-rule="evenodd"></path>
                                    </svg>
                                    Quick Actions
                                </h3>
                                <p class="text-base-content/70 mb-4">Test application functionality and features</p>
                                <div class="flex flex-wrap gap-3">
                                    <button class="btn btn-primary btn-sm gap-2" onclick="testBackendConnection()">
                                        <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                                            <path fill-rule="evenodd" d="M12.395 2.553a1 1 0 00-1.45-.385c-.345.23-.614.558-.822.88-.214.33-.403.713-.57 1.116-.334.804-.614 1.768-.84 2.734a31.365 31.365 0 00-.613 3.58 2.64 2.64 0 01-.945-1.067c-.328-.68-.398-1.534-.398-2.654A1 1 0 005.05 6.05 6.981 6.981 0 003 11a7 7 0 1011.95-4.95c-.592-.591-.98-.985-1.348-1.467-.363-.476-.724-1.063-1.207-2.03zM12.12 15.12A3 3 0 017 13s.879.5 2.5.5c0-1 .5-4 1.25-4.5.5 1 .786 1.293 1.371 1.879A2.99 2.99 0 0113 13a2.99 2.99 0 01-.879 2.121z" clip-rule="evenodd"></path>
                                        </svg>
                                        Test Backend
                                    </button>
                                    <button class="btn btn-secondary btn-sm gap-2" onclick="showNotification()">
                                        <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                                            <path d="M10 2a6 6 0 00-6 6v3.586l-.707.707A1 1 0 004 14h12a1 1 0 00.707-1.707L16 11.586V8a6 6 0 00-6-6zM10 18a3 3 0 01-3-3h6a3 3 0 01-3 3z"></path>
                                        </svg>
                                        Show Notification
                                    </button>
                                    <button class="btn btn-accent btn-sm gap-2" onclick="openDevTools()">
                                        <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                                            <path fill-rule="evenodd" d="M12.316 3.051a1 1 0 01.633 1.265l-4 12a1 1 0 11-1.898-.632l4-12a1 1 0 011.265-.633zM5.707 6.293a1 1 0 010 1.414L3.414 10l2.293 2.293a1 1 0 11-1.414 1.414l-3-3a1 1 0 010-1.414l3-3a1 1 0 011.414 0zm8.586 0a1 1 0 011.414 0l3 3a1 1 0 010 1.414l-3 3a1 1 0 11-1.414-1.414L16.586 10l-2.293-2.293a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                                        </svg>
                                        Dev Tools
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- System Info -->
                        <div class="card bg-base-200 shadow-lg">
                            <div class="card-body">
                                <h3 class="card-title flex items-center gap-2">
                                    <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M3 5a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zM3 10a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zM3 15a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z" clip-rule="evenodd"></path>
                                    </svg>
                                    System Information
                                </h3>
                                <div class="space-y-2 text-sm">
                                    <div class="flex justify-between">
                                        <span class="text-base-content/70">Platform:</span>
                                        <span id="platform-info">Loading...</span>
                                    </div>
                                    <div class="flex justify-between">
                                        <span class="text-base-content/70">Electron:</span>
                                        <span id="electron-info">Loading...</span>
                                    </div>
                                    <div class="flex justify-between">
                                        <span class="text-base-content/70">Node.js:</span>
                                        <span id="node-info">Loading...</span>
                                    </div>
                                    <div class="flex justify-between">
                                        <span class="text-base-content/70">Chrome:</span>
                                        <span id="chrome-info">Loading...</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </section>

                <!-- Features Section -->
                <section id="features-section" class="content-section hidden">
                    <h2 class="text-3xl font-bold mb-6">Features</h2>
                    
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div class="feature-card">
                            <div class="card-body">
                                <h3 class="card-title">Fast Performance</h3>
                                <p>Powered by Bun runtime for lightning-fast JavaScript execution.</p>
                            </div>
                        </div>
                        
                        <div class="feature-card">
                            <div class="card-body">
                                <h3 class="card-title">Cross Platform</h3>
                                <p>Runs on Windows, macOS, and Linux with native feel.</p>
                            </div>
                        </div>
                        
                        <div class="feature-card">
                            <div class="card-body">
                                <h3 class="card-title">Modern UI</h3>
                                <p>Beautiful interface built with DaisyUI components.</p>
                            </div>
                        </div>
                        
                        <div class="feature-card">
                            <div class="card-body">
                                <h3 class="card-title">Secure</h3>
                                <p>Context isolation and secure IPC communication.</p>
                            </div>
                        </div>
                    </div>
                </section>

                <!-- Settings Section -->
                <section id="settings-section" class="content-section hidden">
                    <h2 class="text-3xl font-bold mb-6">Settings</h2>
                    
                    <div class="space-y-6">
                        <div class="card bg-base-200">
                            <div class="card-body">
                                <h3 class="card-title">Appearance</h3>
                                <div class="form-control">
                                    <label class="label">
                                        <span class="label-text">Theme</span>
                                    </label>
                                    <select class="select select-bordered" onchange="setTheme(this.value)">
                                        <option value="light">Light</option>
                                        <option value="dark">Dark</option>
                                        <option value="cupcake">Cupcake</option>
                                        <option value="cyberpunk">Cyberpunk</option>
                                        <option value="dracula">Dracula</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        
                        <div class="card bg-base-200">
                            <div class="card-body">
                                <h3 class="card-title">Preferences</h3>
                                <div class="form-control">
                                    <label class="cursor-pointer label">
                                        <span class="label-text">Enable notifications</span>
                                        <input type="checkbox" class="toggle toggle-primary" checked />
                                    </label>
                                </div>
                                <div class="form-control">
                                    <label class="cursor-pointer label">
                                        <span class="label-text">Auto-save</span>
                                        <input type="checkbox" class="toggle toggle-secondary" checked />
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>
                </section>

                <!-- About Section -->
                <section id="about-section" class="content-section hidden">
                    <h2 class="text-3xl font-bold mb-6">About</h2>
                    
                    <div class="card bg-base-200">
                        <div class="card-body">
                            <h3 class="card-title">Tini Desktop App</h3>
                            <p class="mb-4">A modern desktop application showcasing the power of combining Bun, Electron, and DaisyUI.</p>
                            
                            <div class="stats stats-vertical lg:stats-horizontal shadow">
                                <div class="stat">
                                    <div class="stat-title">Version</div>
                                    <div class="stat-value text-lg" id="app-version">1.0.0</div>
                                </div>
                                
                                <div class="stat">
                                    <div class="stat-title">Platform</div>
                                    <div class="stat-value text-lg" id="app-platform">Unknown</div>
                                </div>
                                
                                <div class="stat">
                                    <div class="stat-title">Electron</div>
                                    <div class="stat-value text-lg" id="electron-version">Unknown</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </section>
            </div>
        </main>

        <!-- Footer -->
        <footer class="app-footer">
            <div class="flex justify-between items-center">
                <span>© 2025 Tini Desktop App</span>
                <span id="status-indicator" class="text-success">Ready</span>
            </div>
        </footer>
    </div>

    <!-- Loading Overlay -->
    <div id="loading-overlay" class="loading-overlay hidden">
        <div class="loading loading-spinner loading-lg"></div>
    </div>

    <!-- Modals -->
    <dialog id="about-modal" class="modal">
        <div class="modal-box">
            <h3 class="font-bold text-lg">About Tini Desktop App</h3>
            <p class="py-4">This application demonstrates the integration of modern web technologies in a desktop environment.</p>
            <div class="modal-action">
                <form method="dialog">
                    <button class="btn">Close</button>
                </form>
            </div>
        </div>
    </dialog>

    <script src="auth-client.js"></script>
    <script src="app.js"></script>
</body>
</html>
