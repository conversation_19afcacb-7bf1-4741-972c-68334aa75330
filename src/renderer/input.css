@import "tailwindcss";
@plugin "daisyui";

/* Custom styles for the desktop app */
@layer base {
  html {
    @apply h-full;
  }
  
  body {
    @apply h-full m-0 p-0 font-sans;
  }

  /* Custom scrollbar */
  ::-webkit-scrollbar {
    @apply w-2;
  }

  ::-webkit-scrollbar-track {
    @apply bg-base-200;
  }

  ::-webkit-scrollbar-thumb {
    @apply bg-base-300 rounded-full;
  }

  ::-webkit-scrollbar-thumb:hover {
    @apply bg-base-content/20;
  }
}

@layer components {
  /* Custom component styles */
  .app-container {
    @apply h-screen flex flex-col bg-base-100;
  }

  .app-header {
    @apply bg-base-200 border-b border-base-300 p-4 flex items-center justify-between;
  }

  .app-main {
    @apply flex-1 flex overflow-hidden;
  }

  .app-sidebar {
    @apply w-64 bg-base-200 border-r border-base-300 p-4 overflow-y-auto;
  }

  .app-content {
    @apply flex-1 p-6 overflow-y-auto;
  }

  .app-footer {
    @apply bg-base-200 border-t border-base-300 p-2 text-sm text-base-content/70;
  }

  /* Card styles */
  .feature-card {
    @apply card bg-base-100 shadow-lg hover:shadow-xl transition-shadow duration-300;
  }

  /* Button variants */
  .btn-gradient {
    @apply btn bg-gradient-to-r from-primary to-secondary text-primary-content border-none;
  }

  .btn-gradient:hover {
    @apply from-primary/80 to-secondary/80;
  }

  /* Loading states */
  .loading-overlay {
    @apply fixed inset-0 bg-base-100/80 backdrop-blur-sm flex items-center justify-center z-50;
  }

  /* Animations */
  .fade-in {
    animation: fadeIn 0.3s ease-in-out;
  }

  .slide-in-right {
    animation: slideInRight 0.3s ease-out;
  }

  .slide-in-left {
    animation: slideInLeft 0.3s ease-out;
  }
}

@layer utilities {
  /* Custom utilities */
  .text-shadow {
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }

  .glass-effect {
    @apply bg-base-100/80 backdrop-blur-md border border-base-300/50;
  }
}

/* Keyframe animations */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideInRight {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes slideInLeft {
  from {
    transform: translateX(-100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

/* Dark mode specific adjustments */
[data-theme="dark"] {
  .glass-effect {
    @apply bg-base-100/60;
  }
}

/* Print styles */
@media print {
  .no-print {
    display: none !important;
  }
}
